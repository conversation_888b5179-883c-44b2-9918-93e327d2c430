#!/usr/bin/env pwsh

# Simple APK Build Script - Alternative Approach
Write-Host "🚀 Ali Duct Factory - Simple APK Build (Alternative Method)" -ForegroundColor Green
Write-Host "=========================================================" -ForegroundColor Green

# Check if device is connected
Write-Host "📱 Checking device connection..." -ForegroundColor Yellow
$devices = adb devices
if ($devices -match "device$") {
    Write-Host "✅ Android device detected" -ForegroundColor Green
} else {
    Write-Host "❌ No Android device detected. Please connect your device." -ForegroundColor Red
    exit 1
}

# Method 1: Try using Expo Development Build
Write-Host "🔧 Method 1: Trying Expo Development Build..." -ForegroundColor Yellow

try {
    # Clean everything first
    Write-Host "🧹 Cleaning project..." -ForegroundColor Cyan
    if (Test-Path "android/app/build") { Remove-Item -Recurse -Force "android/app/build" }
    if (Test-Path "android/build") { Remove-Item -Recurse -Force "android/build" }
    if (Test-Path ".expo") { Remove-Item -Recurse -Force ".expo" }
    
    # Try to build development client
    Write-Host "📦 Building development client..." -ForegroundColor Cyan
    npx expo install --fix
    
    # Use expo prebuild to generate native code
    Write-Host "🔧 Pre-building native code..." -ForegroundColor Cyan
    npx expo prebuild --platform android --clear --no-install
    
    Write-Host "✅ Expo prebuild completed" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️  Expo method failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Method 2: Try React Native CLI directly
Write-Host "🔧 Method 2: Trying React Native CLI..." -ForegroundColor Yellow

try {
    # Install React Native CLI if not present
    Write-Host "📦 Ensuring React Native CLI is available..." -ForegroundColor Cyan
    npm install -g @react-native-community/cli
    
    # Try to build using React Native CLI
    Write-Host "🔨 Building with React Native CLI..." -ForegroundColor Cyan
    npx react-native run-android --variant=debug
    
} catch {
    Write-Host "⚠️  React Native CLI method failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Method 3: Direct Gradle build with simplified configuration
Write-Host "🔧 Method 3: Direct Gradle build..." -ForegroundColor Yellow

try {
    Set-Location "android"
    
    # Clean gradle cache
    Write-Host "🧹 Cleaning Gradle cache..." -ForegroundColor Cyan
    ./gradlew clean
    
    # Try building debug APK directly
    Write-Host "🔨 Building debug APK..." -ForegroundColor Cyan
    ./gradlew assembleDebug --no-daemon --offline
    
    Set-Location ".."
    
} catch {
    Write-Host "⚠️  Direct Gradle method failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Set-Location ".."
}

# Method 4: Check for any existing APK files
Write-Host "🔍 Searching for any existing APK files..." -ForegroundColor Yellow

$apkFiles = Get-ChildItem -Path "." -Filter "*.apk" -Recurse -ErrorAction SilentlyContinue

if ($apkFiles.Count -gt 0) {
    Write-Host "📱 Found APK files:" -ForegroundColor Green
    foreach ($apk in $apkFiles) {
        $size = [math]::Round($apk.Length / 1MB, 2)
        Write-Host "   📦 $($apk.FullName) ($size MB)" -ForegroundColor Cyan

        # Try to install the APK
        Write-Host "📲 Installing APK to device..." -ForegroundColor Yellow
        adb install -r $apk.FullName

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ APK installed successfully!" -ForegroundColor Green
            Write-Host "🎯 You can now launch 'Ali Duct Factory' on your device" -ForegroundColor Green
            break
        } else {
            Write-Host "❌ Failed to install APK" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ No APK files found" -ForegroundColor Red
}

# Final instructions
Write-Host ""
Write-Host "📋 Manual Build Instructions:" -ForegroundColor Yellow
Write-Host "If automatic build failed, try these manual steps:" -ForegroundColor Cyan
Write-Host "1. Open Android Studio" -ForegroundColor Cyan
Write-Host "2. Open the 'android' folder as a project" -ForegroundColor Cyan
Write-Host "3. Let Android Studio sync and download dependencies" -ForegroundColor Cyan
Write-Host "4. Build > Generate Signed Bundle / APK > APK" -ForegroundColor Cyan
Write-Host "5. Choose 'debug' build variant" -ForegroundColor Cyan
Write-Host "6. Install the generated APK on your device" -ForegroundColor Cyan

Write-Host ""
Write-Host "🏁 Build process completed!" -ForegroundColor Green
