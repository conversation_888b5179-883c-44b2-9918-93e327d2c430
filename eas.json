{"cli": {"version": ">= 12.0.0"}, "build": {"preview": {"distribution": "internal", "env": {"EXPO_PUBLIC_SUPABASE_URL": "https://emxbbmsieclwlslwgkua.supabase.co", "EXPO_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs"}, "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "node": {"npmInstallCommand": "npm ci --legacy-peer-deps"}}, "production": {"env": {"EXPO_PUBLIC_SUPABASE_URL": "https://emxbbmsieclwlslwgkua.supabase.co", "EXPO_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs"}, "ios": {"resourceClass": "m-medium"}, "android": {"gradleCommand": ":app:bundleRelease"}, "node": {"npmInstallCommand": "npm ci --legacy-peer-deps"}}, "development": {"node": {"npmInstallCommand": "npm ci --legacy-peer-deps"}}}}