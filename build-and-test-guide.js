/**
 * Comprehensive Build and Testing Guide for Android APK
 * Step-by-step instructions to create a stable, crash-free APK
 */

const fs = require('fs');
const { execSync } = require('child_process');

console.log('📱 Android APK Build and Testing Guide');
console.log('=====================================\n');

// Check prerequisites
function checkPrerequisites() {
  console.log('1️⃣ CHECKING BUILD PREREQUISITES...\n');
  
  const checks = [
    {
      name: 'EAS CLI',
      command: 'eas --version',
      description: 'Expo Application Services CLI'
    },
    {
      name: 'Node.js',
      command: 'node --version',
      description: 'Node.js runtime'
    },
    {
      name: 'npm',
      command: 'npm --version',
      description: 'Node Package Manager'
    }
  ];
  
  let allGood = true;
  
  checks.forEach(check => {
    try {
      const version = execSync(check.command, { encoding: 'utf8' }).trim();
      console.log(`✅ ${check.name}: ${version}`);
    } catch (error) {
      console.log(`❌ ${check.name}: Not installed or not in PATH`);
      console.log(`   Install: ${check.description}`);
      allGood = false;
    }
  });
  
  // Check if logged into EAS
  try {
    execSync('eas whoami', { encoding: 'utf8' });
    console.log('✅ EAS: Logged in');
  } catch (error) {
    console.log('❌ EAS: Not logged in');
    console.log('   Run: eas login');
    allGood = false;
  }
  
  console.log(`\n${allGood ? '✅ All prerequisites met!' : '❌ Please fix prerequisites before continuing'}\n`);
  return allGood;
}

// Clean build environment
function cleanBuildEnvironment() {
  console.log('2️⃣ CLEANING BUILD ENVIRONMENT...\n');
  
  const cleanCommands = [
    {
      name: 'Clear Expo cache',
      command: 'npx expo start --clear',
      description: 'Clears Metro bundler cache'
    },
    {
      name: 'Clear npm cache',
      command: 'npm cache clean --force',
      description: 'Clears npm package cache'
    }
  ];
  
  cleanCommands.forEach(cmd => {
    try {
      console.log(`🧹 ${cmd.name}...`);
      // Note: These commands would be run manually
      console.log(`   Command: ${cmd.command}`);
      console.log(`   Purpose: ${cmd.description}`);
    } catch (error) {
      console.log(`⚠️  Warning: ${cmd.name} failed`);
    }
  });
  
  console.log('✅ Build environment cleaning instructions provided\n');
}

// Verify configuration
function verifyConfiguration() {
  console.log('3️⃣ VERIFYING CONFIGURATION...\n');
  
  try {
    // Check app.json
    const appJson = JSON.parse(fs.readFileSync('./app.json', 'utf8'));
    console.log('📋 App Configuration:');
    console.log(`   Name: ${appJson.expo.name}`);
    console.log(`   Version: ${appJson.expo.version}`);
    console.log(`   Package: ${appJson.expo.android.package}`);
    console.log(`   New Architecture: ${appJson.expo.newArchEnabled ? 'Enabled' : 'Disabled'}`);
    
    // Check eas.json
    const easJson = JSON.parse(fs.readFileSync('./eas.json', 'utf8'));
    console.log('\n🏗️ Build Configuration:');
    console.log(`   Preview build type: ${easJson.build.preview.android.buildType}`);
    console.log(`   Environment variables: ${Object.keys(easJson.build.preview.env).length} configured`);
    
    // Check .env
    if (fs.existsSync('./.env')) {
      const envContent = fs.readFileSync('./.env', 'utf8');
      const hasSupabase = envContent.includes('EXPO_PUBLIC_SUPABASE_URL');
      console.log(`\n🔐 Environment: ${hasSupabase ? 'Supabase configured' : 'No Supabase config'}`);
    }
    
    console.log('\n✅ Configuration verification complete\n');
    return true;
    
  } catch (error) {
    console.log(`❌ Configuration error: ${error.message}\n`);
    return false;
  }
}

// Build instructions
function provideBuildInstructions() {
  console.log('4️⃣ BUILD INSTRUCTIONS...\n');
  
  console.log('📱 STEP-BY-STEP BUILD PROCESS:');
  console.log('==============================\n');
  
  console.log('🔧 STEP 1: Clean and Prepare');
  console.log('   npx expo start --clear');
  console.log('   # Wait for QR code, then press Ctrl+C to stop\n');
  
  console.log('🏗️ STEP 2: Create Preview Build (APK)');
  console.log('   eas build --profile preview --platform android');
  console.log('   # This creates an APK file for testing\n');
  
  console.log('📥 STEP 3: Download and Test APK');
  console.log('   # EAS will provide a download link');
  console.log('   # Install APK on your Android device');
  console.log('   # Test all features thoroughly\n');
  
  console.log('🚀 STEP 4: Production Build (if preview works)');
  console.log('   eas build --profile production --platform android');
  console.log('   # This creates an AAB file for Play Store\n');
  
  console.log('⚠️  IMPORTANT NOTES:');
  console.log('   • Preview builds create APK files (for direct installation)');
  console.log('   • Production builds create AAB files (for Play Store)');
  console.log('   • Always test preview builds before production');
  console.log('   • Build process takes 10-20 minutes');
  console.log('   • You\'ll receive email notification when complete\n');
}

// Testing checklist
function provideTestingChecklist() {
  console.log('5️⃣ TESTING CHECKLIST...\n');
  
  console.log('✅ CRITICAL FEATURES TO TEST:');
  console.log('=============================\n');
  
  const testItems = [
    '📱 App Launch - App opens without crashing',
    '🔐 Authentication - All 4 users can log in (ali, car, wasta1, wasta2)',
    '📊 Dashboard - User-specific screens load correctly',
    '📝 Job Management - Create, view, edit jobs',
    '📸 Camera - Take photos for jobs',
    '🖼️ Image Upload - Upload images to Supabase',
    '📍 Location - Location permissions work',
    '🔄 Data Sync - Real-time updates from database',
    '🌐 Network - App works with/without internet',
    '🔙 Navigation - All screens accessible',
    '🎨 RTL Support - Kurdish text displays correctly',
    '🔄 App Lifecycle - App survives background/foreground'
  ];
  
  testItems.forEach((item, index) => {
    console.log(`${index + 1}.  ${item}`);
  });
  
  console.log('\n🐛 IF APP CRASHES:');
  console.log('==================');
  console.log('1. Enable USB Debugging on your Android device');
  console.log('2. Connect device to computer');
  console.log('3. Run: adb logcat | grep -i "aliduct\\|crash\\|error"');
  console.log('4. Look for specific error messages');
  console.log('5. Check JavaScript errors in Metro logs\n');
}

// Troubleshooting guide
function provideTroubleshootingGuide() {
  console.log('6️⃣ TROUBLESHOOTING GUIDE...\n');
  
  console.log('🔧 COMMON ISSUES AND SOLUTIONS:');
  console.log('===============================\n');
  
  const issues = [
    {
      problem: 'App crashes immediately on launch',
      solutions: [
        'Check package ID matches in app.json and build.gradle',
        'Verify all required permissions in AndroidManifest.xml',
        'Disable New Architecture if enabled',
        'Check for missing environment variables'
      ]
    },
    {
      problem: 'Camera/Image picker crashes',
      solutions: [
        'Verify CAMERA permission in AndroidManifest.xml',
        'Check storage permissions (READ/WRITE_EXTERNAL_STORAGE)',
        'Test on different Android versions',
        'Ensure expo-camera and expo-image-picker are compatible'
      ]
    },
    {
      problem: 'Database connection fails',
      solutions: [
        'Verify Supabase URL and API key in .env',
        'Check internet connectivity',
        'Verify RLS policies allow access',
        'Test with different network conditions'
      ]
    },
    {
      problem: 'Build fails on EAS',
      solutions: [
        'Check EAS build logs for specific errors',
        'Verify all dependencies are compatible',
        'Clear node_modules and reinstall',
        'Check for syntax errors in configuration files'
      ]
    }
  ];
  
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.problem}:`);
    issue.solutions.forEach(solution => {
      console.log(`   • ${solution}`);
    });
    console.log('');
  });
}

// Main function
function runGuide() {
  console.log('🚀 Starting comprehensive build and testing guide...\n');
  
  const steps = [
    checkPrerequisites,
    cleanBuildEnvironment,
    verifyConfiguration,
    provideBuildInstructions,
    provideTestingChecklist,
    provideTroubleshootingGuide
  ];
  
  steps.forEach(step => {
    try {
      step();
    } catch (error) {
      console.log(`❌ Error in step: ${error.message}`);
    }
  });
  
  console.log('🎉 BUILD AND TESTING GUIDE COMPLETE!');
  console.log('\n💡 QUICK START:');
  console.log('1. Run: npx expo start --clear');
  console.log('2. Stop with Ctrl+C when ready');
  console.log('3. Run: eas build --profile preview --platform android');
  console.log('4. Wait for build completion email');
  console.log('5. Download and test APK on your device');
  console.log('\n🔗 Useful Commands:');
  console.log('   eas build:list          # View build history');
  console.log('   eas build:cancel        # Cancel running build');
  console.log('   eas device:create       # Register test device');
}

// Run the guide
runGuide();
