#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 FIXING GRADLE BUILD ISSUES');
console.log('==============================\n');

let fixesApplied = 0;

// Fix 1: Add explicit Android Gradle Plugin version
function fixAndroidGradlePlugin() {
    console.log('1. Fixing Android Gradle Plugin version...');
    
    const buildGradlePath = path.join(__dirname, 'android', 'build.gradle');
    let content = fs.readFileSync(buildGradlePath, 'utf8');
    
    // Replace the classpath line with explicit version
    const oldClasspath = "classpath('com.android.tools.build:gradle')";
    const newClasspath = "classpath('com.android.tools.build:gradle:8.7.3')";
    
    if (content.includes(oldClasspath)) {
        content = content.replace(oldClasspath, newClasspath);
        fs.writeFileSync(buildGradlePath, content);
        console.log('   ✅ Added Android Gradle Plugin version 8.7.3');
        fixesApplied++;
    } else {
        console.log('   ℹ️  Android Gradle Plugin version already specified');
    }
}

// Fix 2: Update React to version 18 for better compatibility
function fixReactVersion() {
    console.log('2. Fixing React version compatibility...');
    
    const packageJsonPath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    if (packageJson.dependencies.react === '19.0.0') {
        packageJson.dependencies.react = '18.3.1';
        packageJson.devDependencies['@types/react'] = '~18.3.12';
        
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        console.log('   ✅ Downgraded React from 19.0.0 to 18.3.1 for better compatibility');
        console.log('   ✅ Updated @types/react to match React 18');
        fixesApplied++;
    } else {
        console.log('   ℹ️  React version is already compatible');
    }
}

// Fix 3: Configure Metro for Node.js polyfills
function fixMetroConfig() {
    console.log('3. Configuring Metro for Node.js polyfills...');
    
    const metroConfigPath = path.join(__dirname, 'metro.config.js');
    
    const metroConfig = `const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configure resolver for Node.js polyfills
config.resolver.alias = {
  ...config.resolver.alias,
  stream: 'stream-browserify',
  buffer: '@craftzdog/react-native-buffer',
};

// Add Node.js polyfills to resolver platforms
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Configure transformer to handle polyfills
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

module.exports = config;
`;

    if (!fs.existsSync(metroConfigPath)) {
        fs.writeFileSync(metroConfigPath, metroConfig);
        console.log('   ✅ Created metro.config.js with Node.js polyfill support');
        fixesApplied++;
    } else {
        const existingConfig = fs.readFileSync(metroConfigPath, 'utf8');
        if (!existingConfig.includes('stream-browserify')) {
            fs.writeFileSync(metroConfigPath, metroConfig);
            console.log('   ✅ Updated metro.config.js with Node.js polyfill support');
            fixesApplied++;
        } else {
            console.log('   ℹ️  Metro config already has polyfill support');
        }
    }
}

// Fix 4: Update JSC flavor to latest version
function fixJSCFlavor() {
    console.log('4. Updating JSC flavor to latest version...');
    
    const buildGradlePath = path.join(__dirname, 'android', 'app', 'build.gradle');
    let content = fs.readFileSync(buildGradlePath, 'utf8');
    
    const oldJSC = "def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'";
    const newJSC = "def jscFlavor = 'io.github.react-native-community:jsc-android:+'";
    
    if (content.includes('2026004.+')) {
        content = content.replace(oldJSC, newJSC);
        fs.writeFileSync(buildGradlePath, content);
        console.log('   ✅ Updated JSC flavor to latest version');
        fixesApplied++;
    } else {
        console.log('   ℹ️  JSC flavor is already using latest version');
    }
}

// Fix 5: Add Gradle wrapper properties for consistency
function fixGradleWrapper() {
    console.log('5. Updating Gradle wrapper properties...');
    
    const gradleWrapperPath = path.join(__dirname, 'android', 'gradle', 'wrapper', 'gradle-wrapper.properties');
    
    if (fs.existsSync(gradleWrapperPath)) {
        let content = fs.readFileSync(gradleWrapperPath, 'utf8');
        
        // Update to Gradle 8.10.2 which is compatible with AGP 8.7.3
        const newGradleVersion = 'distributionUrl=https\\://services.gradle.org/distributions/gradle-8.10.2-all.zip';
        
        if (!content.includes('gradle-8.10.2')) {
            content = content.replace(/distributionUrl=.*/, newGradleVersion);
            fs.writeFileSync(gradleWrapperPath, content);
            console.log('   ✅ Updated Gradle wrapper to version 8.10.2');
            fixesApplied++;
        } else {
            console.log('   ℹ️  Gradle wrapper is already up to date');
        }
    } else {
        console.log('   ⚠️  Gradle wrapper properties file not found');
    }
}

// Fix 6: Optimize Gradle build performance
function optimizeGradleBuild() {
    console.log('6. Optimizing Gradle build performance...');
    
    const gradlePropsPath = path.join(__dirname, 'android', 'gradle.properties');
    let content = fs.readFileSync(gradlePropsPath, 'utf8');
    
    const optimizations = [
        'org.gradle.parallel=true',
        'org.gradle.configureondemand=true',
        'org.gradle.daemon=true',
        'org.gradle.caching=true'
    ];
    
    let modified = false;
    optimizations.forEach(optimization => {
        const [key] = optimization.split('=');
        if (!content.includes(key)) {
            content += `\n${optimization}`;
            modified = true;
        }
    });
    
    if (modified) {
        fs.writeFileSync(gradlePropsPath, content);
        console.log('   ✅ Added Gradle build optimizations');
        fixesApplied++;
    } else {
        console.log('   ℹ️  Gradle optimizations already present');
    }
}

// Main execution
function applyAllFixes() {
    console.log('Starting Gradle issue fixes...\n');
    
    try {
        fixAndroidGradlePlugin();
        fixReactVersion();
        fixMetroConfig();
        fixJSCFlavor();
        fixGradleWrapper();
        optimizeGradleBuild();
        
        console.log('\n🎉 FIXES COMPLETED');
        console.log('==================');
        console.log(`✅ Applied ${fixesApplied} fixes successfully`);
        
        if (fixesApplied > 0) {
            console.log('\n📋 NEXT STEPS:');
            console.log('1. Run: npm install (to update React dependencies)');
            console.log('2. Clean build cache: npx expo run:android --clear');
            console.log('3. Start new EAS build: eas build --profile preview --platform android');
            console.log('\n⚠️  Note: If React was downgraded, run npm install before building');
        }
        
    } catch (error) {
        console.error('❌ Error applying fixes:', error.message);
    }
}

if (require.main === module) {
    applyAllFixes();
}

module.exports = { applyAllFixes };
