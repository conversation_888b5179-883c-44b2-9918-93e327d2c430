const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configure resolver for Node.js polyfills
config.resolver.alias = {
  ...config.resolver.alias,
  stream: 'stream-browserify',
  buffer: '@craftzdog/react-native-buffer',
};

// Add Node.js polyfills to resolver platforms
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Configure transformer to handle polyfills
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

module.exports = config;
