#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔧 Ali Duct Factory - Downgrade to Stable Version & Build');
console.log('=========================================================');

// Read current package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

console.log('📦 Current React Native version:', packageJson.dependencies['react-native']);

// Downgrade to React Native 0.73.9 (known stable version)
console.log('⬇️  Downgrading to React Native 0.73.9...');

// Update package.json with stable versions
packageJson.dependencies['react-native'] = '0.73.9';
packageJson.dependencies['react'] = '18.2.0';

// Remove problematic overrides
if (packageJson.resolutions) {
    delete packageJson.resolutions['@react-native/gradle-plugin'];
}
if (packageJson.overrides) {
    delete packageJson.overrides['@react-native/gradle-plugin'];
}

// Write updated package.json
fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));

console.log('✅ Package.json updated');

// Clean everything
console.log('🧹 Cleaning project...');
try {
    if (fs.existsSync('node_modules')) {
        execSync('rmdir /s /q node_modules', { stdio: 'inherit' });
    }
    if (fs.existsSync('package-lock.json')) {
        fs.unlinkSync('package-lock.json');
    }
    if (fs.existsSync('yarn.lock')) {
        fs.unlinkSync('yarn.lock');
    }
    if (fs.existsSync('android/app/build')) {
        execSync('rmdir /s /q android\\app\\build', { stdio: 'inherit' });
    }
    if (fs.existsSync('android/build')) {
        execSync('rmdir /s /q android\\build', { stdio: 'inherit' });
    }
    if (fs.existsSync('.expo')) {
        execSync('rmdir /s /q .expo', { stdio: 'inherit' });
    }
} catch (error) {
    console.log('⚠️  Some cleanup operations failed, continuing...');
}

console.log('📦 Installing dependencies...');
try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
} catch (error) {
    console.log('❌ Failed to install dependencies:', error.message);
    process.exit(1);
}

// Update Android configuration for React Native 0.73.9
console.log('🔧 Updating Android configuration...');

// Update android/build.gradle
const androidBuildGradle = `
buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.8.10"
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.0.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}
`;

fs.writeFileSync('android/build.gradle', androidBuildGradle);

// Update gradle wrapper
const gradleWrapperProps = `
distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\\://services.gradle.org/distributions/gradle-8.0.2-all.zip
networkTimeout=10000
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
`;

fs.writeFileSync('android/gradle/wrapper/gradle-wrapper.properties', gradleWrapperProps.trim());

// Update gradle.properties
const gradleProps = `
org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m
android.useAndroidX=true
android.enableJetifier=true
newArchEnabled=false
hermesEnabled=true
`;

fs.writeFileSync('android/gradle.properties', gradleProps.trim());

console.log('✅ Android configuration updated');

// Try to build
console.log('🔨 Attempting to build APK...');

try {
    // First try expo prebuild
    console.log('🔧 Running expo prebuild...');
    execSync('npx expo prebuild --platform android --clear', { stdio: 'inherit' });
    
    // Then try to build
    console.log('🔨 Building APK...');
    execSync('npx expo run:android --device', { stdio: 'inherit' });
    
    console.log('✅ Build completed successfully!');
    
} catch (error) {
    console.log('⚠️  Expo build failed, trying direct Gradle build...');
    
    try {
        process.chdir('android');
        execSync('.\\gradlew assembleDebug', { stdio: 'inherit' });
        process.chdir('..');
        
        console.log('✅ Direct Gradle build completed!');
        
        // Look for APK
        const apkPath = 'android/app/build/outputs/apk/debug/app-debug.apk';
        if (fs.existsSync(apkPath)) {
            console.log('📱 APK found at:', apkPath);
            
            // Install APK
            console.log('📲 Installing APK to device...');
            execSync(`adb install -r "${apkPath}"`, { stdio: 'inherit' });
            console.log('✅ APK installed successfully!');
            console.log('🎯 You can now launch "Ali Duct Factory" on your device');
        }
        
    } catch (gradleError) {
        console.log('❌ All build methods failed');
        console.log('📋 Manual steps required:');
        console.log('1. Open Android Studio');
        console.log('2. Open the android folder as a project');
        console.log('3. Let it sync and download dependencies');
        console.log('4. Build > Generate Signed Bundle / APK > APK');
        console.log('5. Choose debug build variant');
    }
}

console.log('🏁 Process completed!');
