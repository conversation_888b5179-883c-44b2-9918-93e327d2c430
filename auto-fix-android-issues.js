/**
 * Automated Android Build Issues Fix Script
 * Automatically fixes critical issues causing app crashes
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Automated Android Build Issues Fix Script');
console.log('============================================\n');

// Fix 1: Package ID Mismatch
function fixPackageIdMismatch() {
  console.log('1️⃣ FIXING PACKAGE ID MISMATCH...\n');
  
  try {
    // Read app.json to get the correct package ID
    const appJsonPath = './app.json';
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    const correctPackageId = appJson.expo.android.package;
    
    console.log(`📦 Using package ID: ${correctPackageId}`);
    
    // Fix build.gradle
    const buildGradlePath = './android/app/build.gradle';
    let buildGradle = fs.readFileSync(buildGradlePath, 'utf8');
    
    // Replace applicationId
    buildGradle = buildGradle.replace(
      /applicationId\s+"[^"]+"/,
      `applicationId "${correctPackageId}"`
    );
    
    // Replace namespace
    buildGradle = buildGradle.replace(
      /namespace\s+"[^"]+"/,
      `namespace "${correctPackageId}"`
    );
    
    fs.writeFileSync(buildGradlePath, buildGradle);
    console.log('✅ Fixed package ID in build.gradle');
    
    // Create backup and update AndroidManifest.xml if needed
    const manifestPath = './android/app/src/main/AndroidManifest.xml';
    let manifest = fs.readFileSync(manifestPath, 'utf8');
    
    // Update package attribute if it exists
    if (manifest.includes('package=')) {
      manifest = manifest.replace(
        /package="[^"]+"/,
        `package="${correctPackageId}"`
      );
      fs.writeFileSync(manifestPath, manifest);
      console.log('✅ Updated package in AndroidManifest.xml');
    }
    
    console.log('✅ Package ID mismatch fixed!\n');
    return true;
    
  } catch (error) {
    console.log(`❌ Error fixing package ID: ${error.message}\n`);
    return false;
  }
}

// Fix 2: Missing Android Permissions
function fixMissingPermissions() {
  console.log('2️⃣ FIXING MISSING ANDROID PERMISSIONS...\n');
  
  try {
    const manifestPath = './android/app/src/main/AndroidManifest.xml';
    let manifest = fs.readFileSync(manifestPath, 'utf8');
    
    // Required permissions that are missing
    const missingPermissions = [
      'android.permission.CAMERA',
      'android.permission.ACCESS_NETWORK_STATE',
      'android.permission.RECORD_AUDIO'
    ];
    
    // Find the position to insert permissions (after existing permissions)
    const internetPermissionIndex = manifest.indexOf('<uses-permission android:name="android.permission.INTERNET"/>');
    
    if (internetPermissionIndex !== -1) {
      let insertPosition = manifest.indexOf('\n', internetPermissionIndex) + 1;
      
      // Add missing permissions
      missingPermissions.forEach(permission => {
        if (!manifest.includes(`android:name="${permission}"`)) {
          const permissionLine = `  <uses-permission android:name="${permission}"/>\n`;
          manifest = manifest.slice(0, insertPosition) + permissionLine + manifest.slice(insertPosition);
          insertPosition += permissionLine.length;
          console.log(`✅ Added permission: ${permission}`);
        }
      });
      
      fs.writeFileSync(manifestPath, manifest);
      console.log('✅ Missing permissions added to AndroidManifest.xml!\n');
      return true;
    } else {
      console.log('❌ Could not find insertion point for permissions\n');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Error fixing permissions: ${error.message}\n`);
    return false;
  }
}

// Fix 3: Disable New Architecture for Stability
function fixNewArchitecture() {
  console.log('3️⃣ DISABLING NEW ARCHITECTURE FOR STABILITY...\n');
  
  try {
    // Fix app.json
    const appJsonPath = './app.json';
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    if (appJson.expo.newArchEnabled) {
      appJson.expo.newArchEnabled = false;
      fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
      console.log('✅ Disabled newArchEnabled in app.json');
    }
    
    // Fix gradle.properties
    const gradlePropsPath = './android/gradle.properties';
    let gradleProps = fs.readFileSync(gradlePropsPath, 'utf8');
    
    if (gradleProps.includes('newArchEnabled=true')) {
      gradleProps = gradleProps.replace('newArchEnabled=true', 'newArchEnabled=false');
      fs.writeFileSync(gradlePropsPath, gradleProps);
      console.log('✅ Disabled newArchEnabled in gradle.properties');
    }
    
    console.log('✅ New Architecture disabled for stability!\n');
    return true;
    
  } catch (error) {
    console.log(`❌ Error disabling New Architecture: ${error.message}\n`);
    return false;
  }
}

// Fix 4: Google Maps API Key Configuration
function fixGoogleMapsConfig() {
  console.log('4️⃣ FIXING GOOGLE MAPS CONFIGURATION...\n');
  
  try {
    const appJsonPath = './app.json';
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    // Remove the placeholder Google Maps config to prevent crashes
    if (appJson.expo.android.config && appJson.expo.android.config.googleMaps) {
      if (appJson.expo.android.config.googleMaps.apiKey === 'YOUR_GOOGLE_MAPS_API_KEY') {
        // Remove the entire googleMaps config to prevent crashes
        delete appJson.expo.android.config.googleMaps;
        
        // If config is now empty, remove it too
        if (Object.keys(appJson.expo.android.config).length === 0) {
          delete appJson.expo.android.config;
        }
        
        fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
        console.log('✅ Removed placeholder Google Maps API key');
        console.log('⚠️  Note: Location features will be limited without a valid API key');
      }
    }
    
    console.log('✅ Google Maps configuration fixed!\n');
    return true;
    
  } catch (error) {
    console.log(`❌ Error fixing Google Maps config: ${error.message}\n`);
    return false;
  }
}

// Additional Fix: Optimize Gradle Configuration
function optimizeGradleConfig() {
  console.log('5️⃣ OPTIMIZING GRADLE CONFIGURATION...\n');
  
  try {
    const gradlePropsPath = './android/gradle.properties';
    let gradleProps = fs.readFileSync(gradlePropsPath, 'utf8');
    
    // Optimize memory settings for better build stability
    if (!gradleProps.includes('org.gradle.jvmargs=-Xmx4096m')) {
      gradleProps = gradleProps.replace(
        /org\.gradle\.jvmargs=-Xmx\d+m[^\n]*/,
        'org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError'
      );
    }
    
    // Ensure Hermes is enabled for better performance
    if (!gradleProps.includes('hermesEnabled=true')) {
      if (gradleProps.includes('hermesEnabled=false')) {
        gradleProps = gradleProps.replace('hermesEnabled=false', 'hermesEnabled=true');
      } else {
        gradleProps += '\nhermesEnabled=true\n';
      }
    }
    
    // Disable animated webp to reduce APK size and potential crashes
    gradleProps = gradleProps.replace('expo.webp.animated=true', 'expo.webp.animated=false');
    
    fs.writeFileSync(gradlePropsPath, gradleProps);
    console.log('✅ Optimized Gradle configuration for stability');
    console.log('✅ Enabled Hermes for better performance');
    console.log('✅ Disabled animated WebP to reduce crashes\n');
    
    return true;
    
  } catch (error) {
    console.log(`❌ Error optimizing Gradle config: ${error.message}\n`);
    return false;
  }
}

// Main fix function
async function runFixes() {
  console.log('🚀 Starting automated fixes for Android build issues...\n');
  
  const fixes = [
    { name: 'Package ID Mismatch', fn: fixPackageIdMismatch },
    { name: 'Missing Permissions', fn: fixMissingPermissions },
    { name: 'New Architecture', fn: fixNewArchitecture },
    { name: 'Google Maps Config', fn: fixGoogleMapsConfig },
    { name: 'Gradle Optimization', fn: optimizeGradleConfig }
  ];
  
  const results = {};
  
  for (const fix of fixes) {
    try {
      results[fix.name] = fix.fn();
    } catch (error) {
      console.log(`❌ Error running ${fix.name}: ${error.message}`);
      results[fix.name] = false;
    }
  }
  
  console.log('📊 FIX RESULTS SUMMARY:');
  console.log('=======================');
  
  let successCount = 0;
  Object.entries(results).forEach(([fixName, success]) => {
    const status = success ? '✅ FIXED' : '❌ FAILED';
    console.log(`${fixName.padEnd(20)}: ${status}`);
    if (success) successCount++;
  });
  
  console.log(`\n🎯 RESULT: ${successCount}/${fixes.length} fixes applied successfully!`);
  
  if (successCount === fixes.length) {
    console.log('\n🎉 ALL FIXES APPLIED SUCCESSFULLY!');
    console.log('\n📱 NEXT STEPS:');
    console.log('1. Clean your build: npx expo run:android --clear');
    console.log('2. Test with development build first');
    console.log('3. Create a preview build: eas build --profile preview --platform android');
    console.log('4. Test the APK on your device');
    console.log('5. If successful, create production build');
  } else {
    console.log('\n⚠️  Some fixes failed. Please check the errors above.');
  }
  
  return successCount === fixes.length;
}

// Run fixes
runFixes().catch(console.error);
