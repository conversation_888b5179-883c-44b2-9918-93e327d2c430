# Global Gradle configuration for Ali Duct Factory
# This ensures all Gradle operations use the D drive cache

# Force Gradle to use D drive for all cache and temporary files
org.gradle.user.home=D:\\.gradle

# Performance optimizations using existing cache
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Use existing distributions and dependencies from D drive
gradle.user.home=D:\\.gradle
org.gradle.cache.dir=D:\\.gradle\\caches
org.gradle.wrapper.dir=D:\\.gradle\\wrapper

# Android specific settings
android.useAndroidX=true
android.enableJetifier=true

# React Native settings
newArchEnabled=false
hermesEnabled=true