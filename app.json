{"expo": {"name": "Ali duct factory", "slug": "aliduct", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "platforms": ["ios", "android"], "scheme": "aliduct", "description": "Kurdish RTL job management application for Ali duct factory", "extra": {"supportsRTL": true, "eas": {"projectId": "8115d3ab-2b78-4496-8ed3-c60f6631876a"}}, "plugins": ["expo-localization", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "ئەم ئەپە پێویستی بە دەسەڵاتی شوێن هەیە بۆ نیشاندانی شوێنی کڕیارەکان لەسەر نەخشە.", "locationWhenInUsePermission": "ئەم ئەپە پێویستی بە دەسەڵاتی شوێن هەیە بۆ نیشاندانی شوێنی کڕیارەکان.", "isIosBackgroundLocationEnabled": false}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#007AFF"}], ["expo-image-picker", {"photosPermission": "ئەم ئەپە پێویستی بە دەسەڵاتی وێنەکان هەیە بۆ هەڵبژاردن و بارکردنی وێنەکانی کارەکان.", "cameraPermission": "ئەم ئەپە پێویستی بە دەسەڵاتی کامێرا هەیە بۆ گرتنی وێنەی کارەکان."}], ["expo-camera", {"cameraPermission": "ئەم ئەپە پێویستی بە دەسەڵاتی کامێرا هەیە بۆ گرتنی وێنەی کارەکان."}]], "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#007AFF"}, "assetBundlePatterns": ["**/*"], "ios": {"bundleIdentifier": "com.aliduct.manager", "buildNumber": "1", "supportsTablet": true, "requireFullScreen": false, "userInterfaceStyle": "light", "infoPlist": {"NSLocationWhenInUseUsageDescription": "ئەم ئەپە پێویستی بە دەسەڵاتی شوێن هەیە بۆ نیشاندانی شوێنی کڕیارەکان لەسەر نەخشە.", "NSCameraUsageDescription": "ئەم ئەپە پێویستی بە دەسەڵاتی کامێرا هەیە بۆ گرتنی وێنەی کارەکان.", "NSPhotoLibraryUsageDescription": "ئەم ئەپە پێویستی بە دەسەڵاتی وێنەکان هەیە بۆ هەڵبژاردنی وێنەکانی کارەکان.", "CFBundleAllowMixedLocalizations": true, "CFBundleDevelopmentRegion": "ku", "CFBundleLocalizations": ["ku", "en", "ar"], "ExpoLocalization_supportsRTL": true}, "config": {"usesNonExemptEncryption": false}}, "android": {"package": "com.aliduct.manager", "versionCode": 1, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.RECORD_AUDIO"], "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#007AFF"}}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}}}