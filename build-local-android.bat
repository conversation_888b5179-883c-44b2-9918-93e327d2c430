@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo   Ali Duct Factory - Local Android Build
echo ========================================
echo.

REM Check prerequisites
echo Checking prerequisites...

REM Check Java
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java not found. Please install Java 17
    pause
    exit /b 1
) else (
    echo ✓ Java found
)

REM Check Android SDK
if "%ANDROID_HOME%"=="" (
    echo ERROR: ANDROID_HOME not set
    pause
    exit /b 1
) else (
    echo ✓ Android SDK: %ANDROID_HOME%
)

REM Check ADB
adb version >nul 2>&1
if errorlevel 1 (
    echo ERROR: ADB not found
    pause
    exit /b 1
) else (
    echo ✓ ADB found
)

echo.
echo Checking for connected Android devices...
adb devices | findstr "device$" >nul
if errorlevel 1 (
    echo WARNING: No Android devices connected
    echo Connect your Android device via USB and enable USB debugging
    echo Or start an Android emulator
) else (
    echo ✓ Android device(s) connected
)

echo.
echo Installing dependencies...
call npm ci
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Fixing Expo dependencies...
call npx expo install --fix
if errorlevel 1 (
    echo ERROR: Failed to fix Expo dependencies
    pause
    exit /b 1
)

echo.
echo Building Android debug APK...
echo This may take several minutes...
call npx expo run:android --variant debug --no-install

if errorlevel 1 (
    echo.
    echo ERROR: Build failed
    echo.
    echo Troubleshooting tips:
    echo 1. Check if Android device is connected
    echo 2. Ensure USB debugging is enabled
    echo 3. Try: npx expo doctor
    echo 4. Clean build: delete android\app\build folder
    pause
    exit /b 1
)

echo.
echo Checking for generated APK...
set APK_PATH=android\app\build\outputs\apk\debug\app-debug.apk

if exist "%APK_PATH%" (
    echo.
    echo ========================================
    echo   BUILD COMPLETED SUCCESSFULLY!
    echo ========================================
    echo.
    echo APK location: %APK_PATH%
    
    for %%A in ("%APK_PATH%") do (
        set /a "size=%%~zA/1024/1024"
        echo APK size: !size! MB
    )
    
    echo.
    echo Next Steps:
    echo 1. Copy APK to your Android device: %APK_PATH%
    echo 2. Enable 'Install from unknown sources' in Android settings
    echo 3. Install the APK file on your device
    echo 4. Launch 'Ali Duct Factory' app
    echo.
    
    REM Ask if user wants to install directly
    adb devices | findstr "device$" >nul
    if not errorlevel 1 (
        echo Device detected. Install APK now? (y/n)
        set /p choice=
        if /i "!choice!"=="y" (
            echo Installing APK...
            adb install -r "%APK_PATH%"
            if not errorlevel 1 (
                echo.
                echo APK installed successfully!
                echo You can now launch 'Ali Duct Factory' on your device
            ) else (
                echo Failed to install APK
            )
        )
    )
    
) else (
    echo ERROR: APK file not found at expected location: %APK_PATH%
    echo Searching for APK files...
    dir /s android\*.apk 2>nul
)

echo.
echo Build process completed!
pause
