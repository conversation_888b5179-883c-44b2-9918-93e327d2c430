# Ali Duct Factory - Build using D drive Gradle cache
# This script ensures all Gradle operations use your existing D:\.gradle cache

Write-Host "🔧 Ali Duct Factory - Build with D Drive Gradle Cache" -ForegroundColor Cyan
Write-Host "======================================================" -ForegroundColor Cyan

# Set environment variables to force <PERSON><PERSON><PERSON> to use D drive
$env:GRADLE_USER_HOME = "D:\.gradle"
$env:GRADLE_CACHE_DIR = "D:\.gradle\caches"
$env:GRADLE_WRAPPER_DIR = "D:\.gradle\wrapper"

Write-Host "📁 Gradle cache directory: $env:GRADLE_USER_HOME" -ForegroundColor Green
Write-Host "📁 Gradle cache dir: $env:GRADLE_CACHE_DIR" -ForegroundColor Green
Write-Host "📁 Gradle wrapper dir: $env:GRADLE_WRAPPER_DIR" -ForegroundColor Green

# Check if D:\.gradle exists
if (Test-Path "D:\.gradle") {
    Write-Host "✅ Found existing Gradle cache at D:\.gradle" -ForegroundColor Green
    $cacheSize = (Get-ChildItem "D:\.gradle" -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum / 1MB
    Write-Host "📊 Cache size: $([math]::Round($cacheSize, 2)) MB" -ForegroundColor Yellow
} else {
    Write-Host "⚠️  D:\.gradle directory not found, will be created" -ForegroundColor Yellow
}

# Check device connection
Write-Host "📱 Checking Android device connection..." -ForegroundColor Cyan
try {
    $devices = adb devices
    if ($devices -match "device$") {
        Write-Host "✅ Android device detected" -ForegroundColor Green
    } else {
        Write-Host "❌ No Android device detected. Please connect your device and enable USB debugging." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ ADB not found. Please ensure Android SDK is installed." -ForegroundColor Red
    exit 1
}

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Cyan
if (Test-Path "android\app\build") {
    Remove-Item -Recurse -Force "android\app\build"
    Write-Host "✅ Cleaned app build directory" -ForegroundColor Green
}
if (Test-Path "android\build") {
    Remove-Item -Recurse -Force "android\build"
    Write-Host "✅ Cleaned root build directory" -ForegroundColor Green
}

# Method 1: Try Expo run:android with environment variables
Write-Host "🔨 Method 1: Building with Expo (using D drive cache)..." -ForegroundColor Cyan
try {
    npx expo run:android --device --no-build-cache
    Write-Host "✅ Expo build completed successfully!" -ForegroundColor Green
    
    # Look for APK
    $apkPath = "android\app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        Write-Host "📱 APK found: $apkPath" -ForegroundColor Green
        Write-Host "🎯 App should be installed on your device!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "⚠️  Expo method failed, trying direct Gradle..." -ForegroundColor Yellow
}

# Method 2: Direct Gradle build with environment variables
Write-Host "🔨 Method 2: Direct Gradle build (using D drive cache)..." -ForegroundColor Cyan
try {
    Set-Location "android"
    
    # Clean with Gradle
    Write-Host "🧹 Gradle clean..." -ForegroundColor Cyan
    .\gradlew clean --gradle-user-home="D:\.gradle"
    
    # Build debug APK
    Write-Host "🔨 Building debug APK..." -ForegroundColor Cyan
    .\gradlew assembleDebug --gradle-user-home="D:\.gradle" --no-daemon --parallel
    
    Set-Location ".."
    
    # Check for APK
    $apkPath = "android\app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        Write-Host "✅ APK built successfully!" -ForegroundColor Green
        Write-Host "📱 APK location: $apkPath" -ForegroundColor Green
        
        # Install APK
        Write-Host "📲 Installing APK to device..." -ForegroundColor Cyan
        adb install -r $apkPath
        Write-Host "✅ APK installed successfully!" -ForegroundColor Green
        Write-Host "🎯 You can now launch 'Ali Duct Factory' on your device" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "❌ APK not found after build" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Direct Gradle build failed: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ".."
}

# Method 3: React Native CLI
Write-Host "🔨 Method 3: React Native CLI build..." -ForegroundColor Cyan
try {
    npx react-native run-android --variant=debug
    Write-Host "✅ React Native CLI build completed!" -ForegroundColor Green
} catch {
    Write-Host "⚠️  React Native CLI method failed" -ForegroundColor Yellow
}

Write-Host "📋 Manual build instructions if automated methods fail:" -ForegroundColor Yellow
Write-Host "1. Open Android Studio" -ForegroundColor White
Write-Host "2. Open the android folder as a project" -ForegroundColor White
Write-Host "3. In Android Studio, go to File > Settings > Build > Gradle" -ForegroundColor White
Write-Host "4. Set Gradle user home to: D:\.gradle" -ForegroundColor White
Write-Host "5. Build > Generate Signed Bundle / APK > APK > Debug" -ForegroundColor White
Write-Host "6. Install the generated APK on your device" -ForegroundColor White

Write-Host "🏁 Build process completed!" -ForegroundColor Cyan
