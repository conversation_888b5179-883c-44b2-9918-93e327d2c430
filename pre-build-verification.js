/**
 * Pre-Build Verification Script
 * Final check before creating Android APK to ensure success
 */

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔍 Pre-Build Verification Script');
console.log('================================\n');

// Test Supabase connection in production mode
async function testSupabaseConnection() {
  console.log('1️⃣ TESTING SUPABASE CONNECTION...\n');
  
  try {
    // Use the same test we used before but with production environment variables
    const { createClient } = require('@supabase/supabase-js');
    
    // Read environment variables from eas.json (production config)
    const easJson = JSON.parse(fs.readFileSync('./eas.json', 'utf8'));
    const prodEnv = easJson.build.preview.env; // Using preview for APK testing
    
    const supabase = createClient(
      prodEnv.EXPO_PUBLIC_SUPABASE_URL,
      prodEnv.EXPO_PUBLIC_SUPABASE_ANON_KEY
    );
    
    // Test basic connection
    const { data, error } = await supabase.from('app_users').select('count').limit(1);
    
    if (error) {
      console.log(`❌ Supabase connection failed: ${error.message}`);
      return false;
    } else {
      console.log('✅ Supabase connection successful');
      
      // Test authentication
      const { data: users } = await supabase.from('app_users').select('username').limit(4);
      console.log(`✅ Found ${users.length} users in database`);
      
      // Test storage
      const { data: buckets } = await supabase.storage.listBuckets();
      console.log(`✅ Storage accessible with ${buckets.length} bucket(s)`);
      
      console.log('✅ All Supabase features working correctly\n');
      return true;
    }
    
  } catch (error) {
    console.log(`❌ Supabase test failed: ${error.message}\n`);
    return false;
  }
}

// Verify TypeScript compilation
function verifyTypeScriptCompilation() {
  console.log('2️⃣ VERIFYING TYPESCRIPT COMPILATION...\n');
  
  try {
    console.log('🔧 Running TypeScript type check...');
    execSync('npx tsc --noEmit', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ TypeScript compilation successful');
    console.log('✅ No type errors found\n');
    return true;
    
  } catch (error) {
    console.log('❌ TypeScript compilation failed:');
    console.log(error.stdout || error.message);
    console.log('\n⚠️  Fix TypeScript errors before building\n');
    return false;
  }
}

// Check critical files exist
function checkCriticalFiles() {
  console.log('3️⃣ CHECKING CRITICAL FILES...\n');
  
  const criticalFiles = [
    { path: './app.json', description: 'App configuration' },
    { path: './eas.json', description: 'EAS build configuration' },
    { path: './package.json', description: 'Package dependencies' },
    { path: './.env', description: 'Environment variables' },
    { path: './App.tsx', description: 'Main app component' },
    { path: './android/app/build.gradle', description: 'Android build config' },
    { path: './android/app/src/main/AndroidManifest.xml', description: 'Android manifest' },
    { path: './lib/supabase.ts', description: 'Supabase client' },
    { path: './contexts/AuthContext.tsx', description: 'Authentication context' }
  ];
  
  let allFilesExist = true;
  
  criticalFiles.forEach(file => {
    if (fs.existsSync(file.path)) {
      console.log(`✅ ${file.description}: ${file.path}`);
    } else {
      console.log(`❌ Missing ${file.description}: ${file.path}`);
      allFilesExist = false;
    }
  });
  
  console.log(`\n${allFilesExist ? '✅ All critical files present' : '❌ Missing critical files'}\n`);
  return allFilesExist;
}

// Verify dependencies
function verifyDependencies() {
  console.log('4️⃣ VERIFYING DEPENDENCIES...\n');
  
  try {
    console.log('📦 Checking for dependency issues...');
    
    // Check if node_modules exists
    if (!fs.existsSync('./node_modules')) {
      console.log('❌ node_modules not found. Run: npm install');
      return false;
    }
    
    // Check package-lock.json exists
    if (!fs.existsSync('./package-lock.json')) {
      console.log('⚠️  package-lock.json not found. Consider running: npm install');
    }
    
    // Run expo doctor to check for issues
    try {
      const doctorOutput = execSync('npx expo doctor', { encoding: 'utf8', stdio: 'pipe' });
      console.log('✅ Expo doctor check passed');
    } catch (error) {
      console.log('⚠️  Expo doctor found issues:');
      console.log(error.stdout || error.message);
    }
    
    console.log('✅ Dependencies verification complete\n');
    return true;
    
  } catch (error) {
    console.log(`❌ Dependency check failed: ${error.message}\n`);
    return false;
  }
}

// Generate build summary
function generateBuildSummary() {
  console.log('5️⃣ BUILD SUMMARY...\n');
  
  try {
    const appJson = JSON.parse(fs.readFileSync('./app.json', 'utf8'));
    const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    const easJson = JSON.parse(fs.readFileSync('./eas.json', 'utf8'));
    
    console.log('📱 BUILD CONFIGURATION SUMMARY:');
    console.log('===============================');
    console.log(`App Name: ${appJson.expo.name}`);
    console.log(`Version: ${appJson.expo.version}`);
    console.log(`Package ID: ${appJson.expo.android.package}`);
    console.log(`Expo SDK: ${packageJson.dependencies.expo}`);
    console.log(`React Native: ${packageJson.dependencies['react-native']}`);
    console.log(`Build Type: ${easJson.build.preview.android.buildType}`);
    console.log(`New Architecture: ${appJson.expo.newArchEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`Hermes: Enabled (from gradle.properties)`);
    
    console.log('\n🔐 ENVIRONMENT VARIABLES:');
    console.log('=========================');
    const envVars = easJson.build.preview.env;
    Object.keys(envVars).forEach(key => {
      const value = envVars[key];
      const maskedValue = key.includes('KEY') ? value.substring(0, 20) + '...' : value;
      console.log(`${key}: ${maskedValue}`);
    });
    
    console.log('\n📋 PERMISSIONS:');
    console.log('===============');
    appJson.expo.android.permissions.forEach(permission => {
      console.log(`• ${permission}`);
    });
    
    console.log('\n✅ Build summary generated\n');
    return true;
    
  } catch (error) {
    console.log(`❌ Error generating build summary: ${error.message}\n`);
    return false;
  }
}

// Main verification function
async function runVerification() {
  console.log('🚀 Starting pre-build verification...\n');
  
  const checks = [
    { name: 'Supabase Connection', fn: testSupabaseConnection },
    { name: 'TypeScript Compilation', fn: verifyTypeScriptCompilation },
    { name: 'Critical Files', fn: checkCriticalFiles },
    { name: 'Dependencies', fn: verifyDependencies },
    { name: 'Build Summary', fn: generateBuildSummary }
  ];
  
  const results = {};
  
  for (const check of checks) {
    try {
      results[check.name] = await check.fn();
    } catch (error) {
      console.log(`❌ ${check.name} failed: ${error.message}`);
      results[check.name] = false;
    }
  }
  
  console.log('📊 VERIFICATION RESULTS:');
  console.log('========================');
  
  let passedCount = 0;
  Object.entries(results).forEach(([checkName, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${checkName.padEnd(25)}: ${status}`);
    if (passed) passedCount++;
  });
  
  const allPassed = passedCount === checks.length;
  console.log(`\n🎯 RESULT: ${passedCount}/${checks.length} checks passed`);
  
  if (allPassed) {
    console.log('\n🎉 ALL VERIFICATIONS PASSED!');
    console.log('\n🚀 READY TO BUILD APK:');
    console.log('======================');
    console.log('1. Run: npx expo start --clear');
    console.log('2. Wait for QR code, then press Ctrl+C');
    console.log('3. Run: eas build --profile preview --platform android');
    console.log('4. Wait for build completion (10-20 minutes)');
    console.log('5. Download APK from the provided link');
    console.log('6. Install and test on your Android device');
    
    console.log('\n📧 You will receive an email when the build is complete.');
    console.log('📱 The APK will be ready for installation and testing.');
    
  } else {
    console.log('\n⚠️  SOME VERIFICATIONS FAILED');
    console.log('Please fix the issues above before building.');
    console.log('Re-run this script after making fixes.');
  }
  
  return allPassed;
}

// Run verification
runVerification().catch(console.error);
