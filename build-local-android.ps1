#!/usr/bin/env pwsh

# Local Android Build Script for Ali Duct Factory
# This script builds the React Native app locally and generates an APK

param(
    [string]$BuildType = "debug",
    [switch]$Clean = $false,
    [switch]$Install = $false
)

Write-Host "🚀 Ali Duct Factory - Local Android Build" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java not found. Please install Java 17" -ForegroundColor Red
    exit 1
}

# Check Android SDK
if (-not $env:ANDROID_HOME) {
    Write-Host "❌ ANDROID_HOME not set" -ForegroundColor Red
    exit 1
} else {
    Write-Host "✅ Android SDK: $env:ANDROID_HOME" -ForegroundColor Green
}

# Check ADB
try {
    $adbVersion = adb version 2>&1 | Select-String "version"
    Write-Host "✅ ADB: $adbVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ ADB not found" -ForegroundColor Red
    exit 1
}

# Check for connected devices
Write-Host "📱 Checking for connected Android devices..." -ForegroundColor Yellow
$devices = adb devices | Select-String "device$"
if ($devices.Count -eq 0) {
    Write-Host "⚠️  No Android devices connected" -ForegroundColor Yellow
    Write-Host "   Connect your Android device via USB and enable USB debugging" -ForegroundColor Cyan
    Write-Host "   Or start an Android emulator" -ForegroundColor Cyan
} else {
    Write-Host "✅ Found $($devices.Count) connected device(s)" -ForegroundColor Green
    $devices | ForEach-Object { Write-Host "   - $($_.Line)" -ForegroundColor Cyan }
}

# Clean if requested
if ($Clean) {
    Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
    
    # Clean Android build directories
    if (Test-Path "android/app/build") {
        Remove-Item -Recurse -Force "android/app/build"
        Write-Host "   Removed android/app/build" -ForegroundColor Cyan
    }
    
    if (Test-Path "android/build") {
        Remove-Item -Recurse -Force "android/build"
        Write-Host "   Removed android/build" -ForegroundColor Cyan
    }
    
    # Clean Expo and Metro cache
    if (Test-Path ".expo") {
        Remove-Item -Recurse -Force ".expo"
        Write-Host "   Removed .expo cache" -ForegroundColor Cyan
    }
    
    if (Test-Path "node_modules/.cache") {
        Remove-Item -Recurse -Force "node_modules/.cache"
        Write-Host "   Removed node_modules cache" -ForegroundColor Cyan
    }
    
    # Clean Metro cache
    Write-Host "   Clearing Metro bundler cache..." -ForegroundColor Cyan
    npx expo start --clear --no-dev --minify | Out-Null
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm ci
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

# Pre-build setup
Write-Host "⚙️  Pre-build setup..." -ForegroundColor Yellow
npx expo install --fix
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to fix Expo dependencies" -ForegroundColor Red
    exit 1
}

# Build the app
Write-Host "🔨 Building Android $BuildType APK..." -ForegroundColor Yellow
Write-Host "   This may take several minutes..." -ForegroundColor Cyan

if ($BuildType -eq "release") {
    npx expo run:android --variant release --no-install
} else {
    npx expo run:android --variant debug --no-install
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    Write-Host "💡 Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "   1. Try running with -Clean flag" -ForegroundColor Cyan
    Write-Host "   2. Check if Android device is connected" -ForegroundColor Cyan
    Write-Host "   3. Ensure USB debugging is enabled" -ForegroundColor Cyan
    Write-Host "   4. Try: npx expo doctor" -ForegroundColor Cyan
    exit 1
}

# Find the generated APK
$apkPath = ""
if ($BuildType -eq "release") {
    $apkPath = "android/app/build/outputs/apk/release/app-release.apk"
} else {
    $apkPath = "android/app/build/outputs/apk/debug/app-debug.apk"
}

if (Test-Path $apkPath) {
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
    Write-Host "📱 APK location: $apkPath" -ForegroundColor Cyan
    
    $apkSize = (Get-Item $apkPath).Length / 1MB
    Write-Host "📊 APK size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
    
    # Install if requested and device is connected
    if ($Install -and $devices.Count -gt 0) {
        Write-Host "📲 Installing APK on connected device..." -ForegroundColor Yellow
        adb install -r $apkPath
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ APK installed successfully!" -ForegroundColor Green
            Write-Host "🎉 You can now launch 'Ali Duct Factory' on your device" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to install APK" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "🎯 Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Copy APK to your Android device: $apkPath" -ForegroundColor Cyan
    Write-Host "2. Enable 'Install from unknown sources' in Android settings" -ForegroundColor Cyan
    Write-Host "3. Install the APK file on your device" -ForegroundColor Cyan
    Write-Host "4. Launch 'Ali Duct Factory' app" -ForegroundColor Cyan
    
} else {
    Write-Host "❌ APK file not found at expected location: $apkPath" -ForegroundColor Red
    Write-Host "🔍 Searching for APK files..." -ForegroundColor Yellow
    Get-ChildItem -Path "android" -Filter "*.apk" -Recurse | ForEach-Object {
        Write-Host "   Found: $($_.FullName)" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "🏁 Build process completed!" -ForegroundColor Green
