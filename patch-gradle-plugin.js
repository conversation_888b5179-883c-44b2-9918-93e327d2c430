#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Patching React Native Gradle Plugin Build File...');

// Find all instances of the problematic build.gradle.kts file
const searchPaths = [
    'node_modules/@react-native/gradle-plugin/react-native-gradle-plugin/build.gradle.kts',
    'node_modules/react-native/node_modules/@react-native/gradle-plugin/react-native-gradle-plugin/build.gradle.kts'
];

let patchedFiles = 0;

searchPaths.forEach(filePath => {
    if (fs.existsSync(filePath)) {
        console.log(`📁 Found problematic file: ${filePath}`);
        
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            
            // Fix the Kotlin compilation errors
            // Replace the problematic allWarningsAsErrors assignment
            const originalLine69 = /allWarningsAsErrors\s*=\s*/g;
            const originalLine70 = /project\.properties\["enableWarningsAsErrors"\]\?\.toString\(\)\?\.toBoolean\(\)\s*\?\:\s*false/g;
            
            // Replace with compatible syntax
            content = content.replace(
                /allWarningsAsErrors\s*=\s*project\.properties\["enableWarningsAsErrors"\]\?\.toString\(\)\?\.toBoolean\(\)\s*\?\:\s*false/g,
                'allWarningsAsErrors.set(project.providers.gradleProperty("enableWarningsAsErrors").map { it.toBoolean() }.orElse(false))'
            );
            
            // Alternative fix if the above doesn't work - comment out the problematic lines
            if (content.includes('allWarningsAsErrors =')) {
                content = content.replace(
                    /(\s*)allWarningsAsErrors\s*=.*$/gm,
                    '$1// allWarningsAsErrors = false // Commented out due to compatibility issue'
                );
            }
            
            // Write the patched content back
            fs.writeFileSync(filePath, content);
            console.log(`✅ Patched: ${filePath}`);
            patchedFiles++;
            
        } catch (error) {
            console.log(`❌ Failed to patch ${filePath}: ${error.message}`);
        }
    }
});

if (patchedFiles === 0) {
    console.log('⚠️  No files found to patch. Let\'s try a different approach...');
    
    // Alternative approach: Use an older React Native version temporarily
    console.log('🔄 Downgrading React Native to a more stable version...');
    
    try {
        // Update package.json to use React Native 0.73.x which has better stability
        const packageJsonPath = 'package.json';
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            // Downgrade React Native and related packages
            packageJson.dependencies['react-native'] = '0.73.9';
            
            if (!packageJson.resolutions) packageJson.resolutions = {};
            if (!packageJson.overrides) packageJson.overrides = {};
            
            packageJson.resolutions['react-native'] = '0.73.9';
            packageJson.resolutions['@react-native/gradle-plugin'] = '0.73.9';
            packageJson.overrides['react-native'] = '0.73.9';
            packageJson.overrides['@react-native/gradle-plugin'] = '0.73.9';
            
            fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
            console.log('✅ Updated package.json with stable React Native version');
        }
        
    } catch (error) {
        console.log(`❌ Failed to update package.json: ${error.message}`);
    }
}

// Clean up build artifacts
console.log('🧹 Cleaning build artifacts...');
const cleanPaths = [
    'android/app/build',
    'android/build',
    'android/.gradle',
    '.expo',
    'node_modules/.cache'
];

cleanPaths.forEach(dirPath => {
    if (fs.existsSync(dirPath)) {
        try {
            fs.rmSync(dirPath, { recursive: true, force: true });
            console.log(`✅ Cleaned: ${dirPath}`);
        } catch (error) {
            console.log(`⚠️  Could not clean ${dirPath}: ${error.message}`);
        }
    }
});

console.log('');
console.log('🎯 Gradle Plugin Patch Complete!');
console.log('');
console.log('Next steps:');
console.log('1. Run: npm install');
console.log('2. Run: npx expo run:android --device');
console.log('');
