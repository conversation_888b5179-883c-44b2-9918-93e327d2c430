#!/bin/bash

# Local Development Build Script for Ali Duct Factory
# This script builds the app locally using Expo development build

set -e

echo "🚀 Starting Local Development Build..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf android/app/build
rm -rf android/build
rm -rf .expo
rm -rf node_modules/.cache

# Ensure dependencies are installed
echo "📦 Installing dependencies..."
npm ci

# Clear Metro cache
echo "🗑️ Clearing Metro cache..."
npx expo start --clear

# Generate development build
echo "🔨 Building development APK..."
npx expo run:android --variant debug

echo "✅ Local development build completed!"
echo "📱 APK location: android/app/build/outputs/apk/debug/app-debug.apk"
