#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSING GRADLE BUILD ISSUES');
console.log('=====================================\n');

// Common Gradle build issues and their fixes
const issues = [];

// 1. Check Android Gradle Plugin version
function checkAndroidGradlePlugin() {
    const buildGradlePath = path.join(__dirname, 'android', 'build.gradle');
    const content = fs.readFileSync(buildGradlePath, 'utf8');
    
    if (!content.includes('com.android.tools.build:gradle:')) {
        issues.push({
            type: 'CRITICAL',
            issue: 'Missing Android Gradle Plugin version specification',
            description: 'The Android Gradle Plugin version is not explicitly specified',
            fix: 'Add explicit AGP version in android/build.gradle'
        });
    }
    
    console.log('✅ Android Gradle Plugin check completed');
}

// 2. Check React Native version compatibility
function checkReactNativeCompatibility() {
    const packageJsonPath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const reactNativeVersion = packageJson.dependencies['react-native'];
    const expoVersion = packageJson.dependencies['expo'];
    
    console.log(`📱 React Native version: ${reactNativeVersion}`);
    console.log(`🚀 Expo SDK version: ${expoVersion}`);
    
    // Check for React 19 compatibility issues
    if (packageJson.dependencies.react === '19.0.0') {
        issues.push({
            type: 'WARNING',
            issue: 'React 19 compatibility concern',
            description: 'React 19 may have compatibility issues with some React Native libraries',
            fix: 'Consider downgrading to React 18 for better stability'
        });
    }
    
    console.log('✅ React Native compatibility check completed');
}

// 3. Check Gradle memory settings
function checkGradleMemorySettings() {
    const gradlePropsPath = path.join(__dirname, 'android', 'gradle.properties');
    const content = fs.readFileSync(gradlePropsPath, 'utf8');
    
    const jvmArgsMatch = content.match(/org\.gradle\.jvmargs=(.+)/);
    if (jvmArgsMatch) {
        const jvmArgs = jvmArgsMatch[1];
        console.log(`💾 Current JVM args: ${jvmArgs}`);
        
        if (!jvmArgs.includes('-Xmx4096m')) {
            issues.push({
                type: 'MEDIUM',
                issue: 'Insufficient heap memory allocation',
                description: 'Gradle may run out of memory during build',
                fix: 'Increase heap memory to -Xmx4096m or higher'
            });
        }
    }
    
    console.log('✅ Gradle memory settings check completed');
}

// 4. Check for dependency conflicts
function checkDependencyConflicts() {
    const packageJsonPath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Check for known problematic combinations
    const deps = packageJson.dependencies;
    
    // Check for stream-browserify which can cause issues
    if (deps['stream-browserify']) {
        issues.push({
            type: 'MEDIUM',
            issue: 'Potential Metro bundler conflict with stream-browserify',
            description: 'stream-browserify can cause bundling issues in React Native',
            fix: 'Configure Metro to handle Node.js polyfills properly'
        });
    }
    
    console.log('✅ Dependency conflicts check completed');
}

// 5. Check Android SDK and build tools versions
function checkAndroidSDKVersions() {
    const buildGradlePath = path.join(__dirname, 'android', 'app', 'build.gradle');
    const content = fs.readFileSync(buildGradlePath, 'utf8');
    
    // Check for hardcoded SDK versions that might be outdated
    if (content.includes('compileSdk') && !content.includes('rootProject.ext.compileSdkVersion')) {
        issues.push({
            type: 'MEDIUM',
            issue: 'Hardcoded Android SDK versions',
            description: 'Using hardcoded SDK versions can cause compatibility issues',
            fix: 'Use rootProject.ext.compileSdkVersion for consistency'
        });
    }
    
    console.log('✅ Android SDK versions check completed');
}

// 6. Check for JSC flavor issues
function checkJSCFlavor() {
    const buildGradlePath = path.join(__dirname, 'android', 'app', 'build.gradle');
    const content = fs.readFileSync(buildGradlePath, 'utf8');
    
    if (content.includes('jsc-android:2026004.+')) {
        issues.push({
            type: 'LOW',
            issue: 'Potentially outdated JSC flavor',
            description: 'The JSC Android flavor version might be outdated',
            fix: 'Update to latest JSC Android version'
        });
    }
    
    console.log('✅ JSC flavor check completed');
}

// Run all checks
function runDiagnostics() {
    console.log('Starting comprehensive Gradle diagnostics...\n');
    
    try {
        checkAndroidGradlePlugin();
        checkReactNativeCompatibility();
        checkGradleMemorySettings();
        checkDependencyConflicts();
        checkAndroidSDKVersions();
        checkJSCFlavor();
        
        console.log('\n📊 DIAGNOSTIC RESULTS');
        console.log('=====================\n');
        
        if (issues.length === 0) {
            console.log('✅ No major issues detected!');
        } else {
            issues.forEach((issue, index) => {
                console.log(`${index + 1}. [${issue.type}] ${issue.issue}`);
                console.log(`   Description: ${issue.description}`);
                console.log(`   Fix: ${issue.fix}\n`);
            });
        }
        
        console.log(`\n🔍 Total issues found: ${issues.length}`);
        console.log('📝 Generating fix script...\n');
        
        return issues;
        
    } catch (error) {
        console.error('❌ Error during diagnostics:', error.message);
        return [];
    }
}

// Export for use in fix script
if (require.main === module) {
    runDiagnostics();
} else {
    module.exports = { runDiagnostics, issues };
}
