#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Patching ALL React Native Gradle Plugin Build Files...');

// Find all build.gradle.kts files in the React Native Gradle plugin directories
function findGradleFiles(dir) {
    const files = [];
    
    function searchDir(currentDir) {
        if (!fs.existsSync(currentDir)) return;
        
        try {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    searchDir(fullPath);
                } else if (item === 'build.gradle.kts') {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            // Ignore permission errors
        }
    }
    
    searchDir(dir);
    return files;
}

// Search for all gradle plugin files
const searchDirs = [
    'node_modules/@react-native/gradle-plugin',
    'node_modules/react-native/node_modules/@react-native/gradle-plugin'
];

let allGradleFiles = [];
searchDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
        const files = findGradleFiles(dir);
        allGradleFiles = allGradleFiles.concat(files);
    }
});

console.log(`📁 Found ${allGradleFiles.length} Gradle build files to check`);

let patchedFiles = 0;

allGradleFiles.forEach(filePath => {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Check if this file has the problematic allWarningsAsErrors line
        if (content.includes('allWarningsAsErrors =')) {
            console.log(`🔍 Patching: ${filePath}`);
            
            // Comment out the problematic lines
            content = content.replace(
                /(\s*)allWarningsAsErrors\s*=.*$/gm,
                '$1// allWarningsAsErrors = false // Commented out due to Kotlin compatibility issue'
            );
            
            // Also handle any related property access patterns
            content = content.replace(
                /project\.properties\["enableWarningsAsErrors"\]\?\.toString\(\)\?\.toBoolean\(\)\s*\?\:\s*false/g,
                'false // Simplified due to compatibility issue'
            );
            
            fs.writeFileSync(filePath, content);
            console.log(`✅ Successfully patched: ${filePath}`);
            patchedFiles++;
        }
        
    } catch (error) {
        console.log(`❌ Failed to patch ${filePath}: ${error.message}`);
    }
});

console.log(`\n🎯 Patched ${patchedFiles} files successfully!`);

// Clean build artifacts
console.log('\n🧹 Cleaning build artifacts...');
const cleanPaths = [
    'android/app/build',
    'android/build',
    'android/.gradle',
    '.expo'
];

cleanPaths.forEach(dirPath => {
    if (fs.existsSync(dirPath)) {
        try {
            fs.rmSync(dirPath, { recursive: true, force: true });
            console.log(`✅ Cleaned: ${dirPath}`);
        } catch (error) {
            console.log(`⚠️  Could not clean ${dirPath}: ${error.message}`);
        }
    }
});

console.log('\n🎯 All Gradle Plugin Files Patched!');
console.log('\nNext step: npx expo run:android --device');
console.log('');
