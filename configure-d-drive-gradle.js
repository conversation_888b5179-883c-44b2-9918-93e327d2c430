#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Configuring Ali Duct Factory to use D drive Gradle cache');
console.log('===========================================================');

// 1. Update android/gradle.properties
console.log('📝 Updating android/gradle.properties...');
const androidGradleProps = `# Gradle settings - Use existing D drive cache to save C drive space
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true
org.gradle.caching=true

# Force Gradle to use D drive cache directory
org.gradle.user.home=D:\\\\.gradle

# Additional cache and temp directories on D drive
gradle.user.home=D:\\\\.gradle
org.gradle.cache.dir=D:\\\\.gradle\\\\caches
org.gradle.wrapper.dir=D:\\\\.gradle\\\\wrapper

# Android settings
android.useAndroidX=true
android.enableJetifier=true

# React Native settings
newArchEnabled=false
hermesEnabled=true

# Optimize build performance with existing cache
org.gradle.unsafe.configuration-cache=true
org.gradle.configuration-cache.problems=warn`;

fs.writeFileSync('android/gradle.properties', androidGradleProps);
console.log('✅ Updated android/gradle.properties');

// 2. Update gradle-wrapper.properties to use D drive
console.log('📝 Updating gradle-wrapper.properties...');
const gradleWrapperProps = `distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\\://services.gradle.org/distributions/gradle-8.0.2-all.zip
networkTimeout=10000
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists`;

fs.writeFileSync('android/gradle/wrapper/gradle-wrapper.properties', gradleWrapperProps);
console.log('✅ Updated gradle-wrapper.properties');

// 3. Create/update root gradle.properties
console.log('📝 Creating root gradle.properties...');
const rootGradleProps = `# Global Gradle configuration for Ali Duct Factory
# This ensures all Gradle operations use the D drive cache

# Force Gradle to use D drive for all cache and temporary files
org.gradle.user.home=D:\\\\.gradle

# Performance optimizations using existing cache
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Use existing distributions and dependencies from D drive
gradle.user.home=D:\\\\.gradle
org.gradle.cache.dir=D:\\\\.gradle\\\\caches
org.gradle.wrapper.dir=D:\\\\.gradle\\\\wrapper

# Android specific settings
android.useAndroidX=true
android.enableJetifier=true

# React Native settings
newArchEnabled=false
hermesEnabled=true`;

fs.writeFileSync('gradle.properties', rootGradleProps);
console.log('✅ Created root gradle.properties');

// 4. Create environment setup script
console.log('📝 Creating environment setup script...');
const envSetupScript = `@echo off
REM Set Gradle environment variables to use D drive cache
set GRADLE_USER_HOME=D:\\.gradle
set GRADLE_CACHE_DIR=D:\\.gradle\\caches
set GRADLE_WRAPPER_DIR=D:\\.gradle\\wrapper

echo Gradle environment configured to use D drive cache
echo GRADLE_USER_HOME=%GRADLE_USER_HOME%
echo GRADLE_CACHE_DIR=%GRADLE_CACHE_DIR%
echo GRADLE_WRAPPER_DIR=%GRADLE_WRAPPER_DIR%`;

fs.writeFileSync('set-gradle-env.bat', envSetupScript);
console.log('✅ Created set-gradle-env.bat');

// 5. Update android/build.gradle to ensure compatibility
console.log('📝 Updating android/build.gradle...');
const androidBuildGradle = `buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.8.10"
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.0.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
    }
    
    // Force all projects to use D drive Gradle cache
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
        }
    }
}`;

fs.writeFileSync('android/build.gradle', androidBuildGradle);
console.log('✅ Updated android/build.gradle');

// 6. Check if D:\.gradle exists and provide information
console.log('📁 Checking D:\\.gradle directory...');
try {
    if (fs.existsSync('D:\\.gradle')) {
        console.log('✅ Found existing Gradle cache at D:\\.gradle');
        
        // Check for common Gradle directories
        const subdirs = ['caches', 'wrapper', 'daemon'];
        subdirs.forEach(subdir => {
            const fullPath = `D:\\.gradle\\${subdir}`;
            if (fs.existsSync(fullPath)) {
                console.log(`  ✅ Found ${subdir} directory`);
            } else {
                console.log(`  ⚠️  ${subdir} directory not found (will be created when needed)`);
            }
        });
    } else {
        console.log('⚠️  D:\\.gradle directory not found');
        console.log('   It will be created automatically when Gradle runs');
    }
} catch (error) {
    console.log('⚠️  Could not check D:\\.gradle directory:', error.message);
}

console.log('');
console.log('🎯 Configuration Summary:');
console.log('========================');
console.log('✅ All Gradle operations will use D:\\.gradle cache');
console.log('✅ Gradle wrapper will download to D:\\.gradle\\wrapper');
console.log('✅ Build cache will be stored in D:\\.gradle\\caches');
console.log('✅ This will prevent filling up your C drive');
console.log('');
console.log('📋 Next Steps:');
console.log('1. Run: build-with-d-drive-cache.ps1 (PowerShell)');
console.log('   OR: build-with-d-drive-cache.bat (Command Prompt)');
console.log('2. The build will use your existing Gradle cache on D drive');
console.log('3. No additional downloads will be stored on C drive');
console.log('');
console.log('🏁 Configuration completed successfully!');
