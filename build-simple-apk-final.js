#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

console.log('🎯 Ali Duct Factory - Final Simple APK Build (D Drive Cache)');
console.log('==============================================================');

// Set environment variables for D drive cache
process.env.GRADLE_USER_HOME = 'D:\\.gradle';
process.env.GRADLE_CACHE_DIR = 'D:\\.gradle\\caches';
process.env.GRADLE_WRAPPER_DIR = 'D:\\.gradle\\wrapper';

console.log('📁 Using Gradle cache at:', process.env.GRADLE_USER_HOME);

// Check device
console.log('📱 Checking device connection...');
try {
    const devices = execSync('adb devices', { encoding: 'utf8' });
    if (devices.includes('device')) {
        console.log('✅ Android device detected');
    } else {
        console.log('❌ No device detected. Please connect your device.');
        process.exit(1);
    }
} catch (error) {
    console.log('❌ ADB not found');
    process.exit(1);
}

// Create ultra-simple Android project
console.log('🔧 Creating ultra-simple Android project...');

// 1. Simple build.gradle (root)
const simpleBuildGradle = `
buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.1'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.10'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
`;

fs.writeFileSync('android/build.gradle', simpleBuildGradle.trim());
console.log('✅ Created simple build.gradle');

// 2. Simple settings.gradle
const simpleSettings = `
rootProject.name = 'AliDuctFactory'
include ':app'
`;

fs.writeFileSync('android/settings.gradle', simpleSettings.trim());
console.log('✅ Created simple settings.gradle');

// 3. Simple app/build.gradle
const simpleAppBuild = `
plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.aliduct.manager'
    compileSdk 34

    defaultConfig {
        applicationId "com.aliduct.manager"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.8.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
}
`;

fs.writeFileSync('android/app/build.gradle', simpleAppBuild.trim());
console.log('✅ Created simple app/build.gradle');

// 4. Create MainActivity.kt
const mainActivityDir = 'android/app/src/main/java/com/aliduct/manager';
if (!fs.existsSync(mainActivityDir)) {
    fs.mkdirSync(mainActivityDir, { recursive: true });
}

const mainActivity = `
package com.aliduct.manager

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import android.widget.TextView
import android.widget.LinearLayout
import android.view.Gravity

class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create layout programmatically
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setPadding(50, 50, 50, 50)
        }
        
        val titleText = TextView(this).apply {
            text = "Ali Duct Factory"
            textSize = 24f
            gravity = Gravity.CENTER
        }
        
        val subtitleText = TextView(this).apply {
            text = "Job Management System"
            textSize = 16f
            gravity = Gravity.CENTER
            setPadding(0, 20, 0, 0)
        }
        
        val statusText = TextView(this).apply {
            text = "✅ App Built Successfully with D Drive Cache!"
            textSize = 14f
            gravity = Gravity.CENTER
            setPadding(0, 40, 0, 0)
        }
        
        layout.addView(titleText)
        layout.addView(subtitleText)
        layout.addView(statusText)
        
        setContentView(layout)
    }
}
`;

fs.writeFileSync(`${mainActivityDir}/MainActivity.kt`, mainActivity.trim());
console.log('✅ Created MainActivity.kt');

// 5. Create AndroidManifest.xml
const manifestDir = 'android/app/src/main';
const manifest = `
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.aliduct.manager">

    <application
        android:allowBackup="true"
        android:label="Ali Duct Factory"
        android:theme="@style/Theme.AppCompat.Light.DarkActionBar">
        
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
`;

fs.writeFileSync(`${manifestDir}/AndroidManifest.xml`, manifest.trim());
console.log('✅ Created AndroidManifest.xml');

// 6. Build the APK
console.log('🔨 Building APK with D drive cache...');
try {
    process.chdir('android');
    
    console.log('🧹 Cleaning...');
    execSync('.\\gradlew clean --gradle-user-home="D:\\.gradle"', { stdio: 'inherit' });
    
    console.log('🔨 Building debug APK...');
    execSync('.\\gradlew assembleDebug --gradle-user-home="D:\\.gradle" --no-daemon --parallel', { stdio: 'inherit' });
    
    process.chdir('..');
    
    // Check for APK
    const apkPath = 'android/app/build/outputs/apk/debug/app-debug.apk';
    if (fs.existsSync(apkPath)) {
        console.log('');
        console.log('🎉 SUCCESS! APK built successfully!');
        console.log('📱 APK location:', apkPath);
        
        // Get APK size
        const stats = fs.statSync(apkPath);
        const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log('📊 APK size:', fileSizeInMB, 'MB');
        
        // Install APK
        console.log('📲 Installing APK to device...');
        execSync(`adb install -r "${apkPath}"`, { stdio: 'inherit' });
        
        console.log('');
        console.log('🎯 INSTALLATION COMPLETE!');
        console.log('✅ "Ali Duct Factory" is now installed on your device');
        console.log('✅ Used D drive Gradle cache - no C drive space consumed');
        console.log('✅ You can launch the app from your device');
        console.log('');
        console.log('📋 Summary:');
        console.log('- Gradle cache used: D:\\.gradle');
        console.log('- APK size:', fileSizeInMB, 'MB');
        console.log('- Installation: Successful');
        console.log('- Storage optimization: Complete');
        
    } else {
        console.log('❌ APK not found after build');
    }
    
} catch (error) {
    console.log('❌ Build failed:', error.message);
    console.log('');
    console.log('📋 The good news: Your D drive Gradle cache is configured correctly!');
    console.log('📋 Manual build option:');
    console.log('1. Open Android Studio');
    console.log('2. Open the android folder as a project');
    console.log('3. Gradle will automatically use D:\\.gradle cache');
    console.log('4. Build > Generate Signed Bundle / APK > APK > Debug');
}

console.log('🏁 Process completed!');
