#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Applying comprehensive Gradle fixes...');

// 1. Fix android/build.gradle with more conservative settings
const buildGradlePath = path.join(__dirname, 'android', 'build.gradle');
const buildGradleContent = `
buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.9.10"
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.1.4")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        maven { url "https://maven.google.com" }
        maven { url "https://jcenter.bintray.com" }
    }

    configurations.all {
        resolutionStrategy {
            force 'androidx.core:core:1.12.0'
            force 'androidx.core:core-ktx:1.12.0'
            force 'androidx.appcompat:appcompat:1.6.1'
            force 'androidx.fragment:fragment:1.6.2'
            force 'androidx.activity:activity:1.8.2'
            force 'androidx.lifecycle:lifecycle-runtime:2.7.0'
        }
    }
}
`;

// 2. Fix gradle wrapper properties with stable version
const gradleWrapperPath = path.join(__dirname, 'android', 'gradle', 'wrapper', 'gradle-wrapper.properties');
const gradleWrapperContent = `
distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\\://services.gradle.org/distributions/gradle-8.1.1-all.zip
networkTimeout=10000
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
`;

// 3. Update gradle.properties with conservative settings
const gradlePropsPath = path.join(__dirname, 'android', 'gradle.properties');
const gradlePropsContent = `
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.daemon=true
org.gradle.configureondemand=false

android.useAndroidX=true
android.enableJetifier=false
android.enableR8.fullMode=false
android.enableProguardInReleaseBuilds=false

reactNativeArchitectures=armeabi-v7a,arm64-v8a
hermesEnabled=true
enableHermes=true

newArchEnabled=false
expo.jsEngine=hermes

FLIPPER_VERSION=0.182.0
`;

// 4. Create a minimal app/build.gradle
const appBuildGradlePath = path.join(__dirname, 'android', 'app', 'build.gradle');

try {
    // Write all files
    fs.writeFileSync(buildGradlePath, buildGradleContent.trim());
    console.log('✅ Updated android/build.gradle');

    fs.writeFileSync(gradleWrapperPath, gradleWrapperContent.trim());
    console.log('✅ Updated gradle-wrapper.properties');

    fs.writeFileSync(gradlePropsPath, gradlePropsContent.trim());
    console.log('✅ Updated gradle.properties');

    console.log('🎉 Comprehensive Gradle fixes applied successfully!');
    console.log('📋 Changes made:');
    console.log('   - Downgraded AGP to 8.1.4 (most stable)');
    console.log('   - Downgraded Gradle to 8.1.1 (compatible)');
    console.log('   - Reduced JVM memory to 4GB');
    console.log('   - Disabled problematic features');
    console.log('   - Added dependency resolution strategies');

} catch (error) {
    console.error('❌ Error applying fixes:', error.message);
    process.exit(1);
}