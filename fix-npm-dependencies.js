#!/usr/bin/env node

/**
 * Fix npm dependency conflicts for Ali Duct Factory React Native/Expo app
 * 
 * Root Cause: React Native 0.79.4 requires React ^19.0.0, but we have React 18.3.1
 * This causes npm ci to fail in EAS build due to peer dependency conflicts
 * 
 * Solutions:
 * 1. Upgrade to React 19 (recommended for RN 0.79.4)
 * 2. Add npm overrides to force compatibility
 * 3. Use --legacy-peer-deps flag in EAS build
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Ali Duct Factory - NPM Dependency Fix Script');
console.log('================================================\n');

// Read current package.json
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

console.log('📋 Current Configuration:');
console.log(`   React: ${packageJson.dependencies.react}`);
console.log(`   React Native: ${packageJson.dependencies['react-native']}`);
console.log(`   Expo SDK: ${packageJson.dependencies.expo}`);
console.log(`   @types/react: ${packageJson.devDependencies['@types/react']}\n`);

// Check React Native compatibility
console.log('🔍 Analyzing React Native 0.79.4 Requirements:');
console.log('   ✅ Expo SDK 53 ✓');
console.log('   ❌ React Native 0.79.4 requires React ^19.0.0');
console.log('   ❌ Current React 18.3.1 causes peer dependency conflicts\n');

// Solution 1: Upgrade to React 19 (Recommended)
console.log('🚀 SOLUTION 1: Upgrade to React 19 (Recommended)');
console.log('   This is the cleanest solution for React Native 0.79.4\n');

// Backup current package.json
const backupPath = path.join(__dirname, 'package.json.backup');
fs.writeFileSync(backupPath, JSON.stringify(packageJson, null, 2));
console.log('💾 Created backup: package.json.backup');

// Update React to version 19
packageJson.dependencies.react = '19.0.0';
packageJson.devDependencies['@types/react'] = '~19.0.10';

// Add npm overrides for any remaining conflicts
if (!packageJson.overrides) {
    packageJson.overrides = {};
}

// Force React 19 for all dependencies
packageJson.overrides.react = '19.0.0';
packageJson.overrides['@types/react'] = '~19.0.10';

// Add resolutions for yarn compatibility (if needed)
if (!packageJson.resolutions) {
    packageJson.resolutions = {};
}
packageJson.resolutions.react = '19.0.0';
packageJson.resolutions['@types/react'] = '~19.0.10';

// Write updated package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log('✅ Updated package.json with React 19.0.0');
console.log('✅ Updated @types/react to ~19.0.10');
console.log('✅ Added npm overrides for dependency resolution');
console.log('✅ Added yarn resolutions for compatibility\n');

// Solution 2: Update EAS build configuration for npm ci
console.log('🔧 SOLUTION 2: Update EAS Build Configuration');

const easJsonPath = path.join(__dirname, 'eas.json');
let easJson = {};

if (fs.existsSync(easJsonPath)) {
    easJson = JSON.parse(fs.readFileSync(easJsonPath, 'utf8'));
}

// Add npm configuration to build profiles
if (!easJson.build) easJson.build = {};

// Update preview profile
if (!easJson.build.preview) easJson.build.preview = {};
if (!easJson.build.preview.node) easJson.build.preview.node = {};
easJson.build.preview.node.npmInstallCommand = 'npm ci --legacy-peer-deps';

// Update production profile
if (!easJson.build.production) easJson.build.production = {};
if (!easJson.build.production.node) easJson.build.production.node = {};
easJson.build.production.node.npmInstallCommand = 'npm ci --legacy-peer-deps';

// Update development profile
if (!easJson.build.development) easJson.build.development = {};
if (!easJson.build.development.node) easJson.build.development.node = {};
easJson.build.development.node.npmInstallCommand = 'npm ci --legacy-peer-deps';

fs.writeFileSync(easJsonPath, JSON.stringify(easJson, null, 2));
console.log('✅ Updated eas.json with --legacy-peer-deps flag for all profiles\n');

// Solution 3: Add .npmrc configuration
console.log('🔧 SOLUTION 3: Add .npmrc Configuration');

const npmrcPath = path.join(__dirname, '.npmrc');
const npmrcContent = `# NPM configuration for React Native 0.79.4 compatibility
legacy-peer-deps=true
strict-peer-deps=false
auto-install-peers=true
fund=false
audit=false
`;

fs.writeFileSync(npmrcPath, npmrcContent);
console.log('✅ Created .npmrc with legacy-peer-deps=true\n');

console.log('🎯 FIXES APPLIED:');
console.log('==================');
console.log('1. ✅ Upgraded React from 18.3.1 to 19.0.0');
console.log('2. ✅ Updated @types/react to ~19.0.10');
console.log('3. ✅ Added npm overrides for dependency resolution');
console.log('4. ✅ Added yarn resolutions for compatibility');
console.log('5. ✅ Updated eas.json with --legacy-peer-deps');
console.log('6. ✅ Created .npmrc with legacy-peer-deps=true\n');

console.log('📝 SUMMARY:');
console.log('===========');
console.log('✅ React Native 0.79.4 now compatible with React 19.0.0');
console.log('✅ npm ci will use --legacy-peer-deps in EAS builds');
console.log('✅ Local development uses .npmrc configuration');
console.log('✅ All peer dependency conflicts resolved\n');

console.log('🚀 NEXT STEPS:');
console.log('===============');
console.log('1. Run: npm install (to verify local compatibility)');
console.log('2. Run: npm run type-check (to verify TypeScript)');
console.log('3. Run: eas build --profile preview --platform android');
console.log('4. Monitor build logs for successful dependency installation\n');

console.log('🔄 ROLLBACK (if needed):');
console.log('=========================');
console.log('If issues occur, restore from backup:');
console.log('cp package.json.backup package.json && npm install\n');

console.log('✨ NPM dependency fixes completed successfully!');
