<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#007AFF</color>
    <color name="iconBackground">#007AFF</color>
    <color name="notification_icon_color">#007AFF</color>
    <color name="splashscreen_background">#007AFF</color>
    <string name="ExpoLocalization_supportsRTL" translatable="false">true</string>
    <string name="app_name">Ali duct factory</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="android:windowOptOutEdgeToEdgeEnforcement" ns1:targetApi="35">true</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#007AFF</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>