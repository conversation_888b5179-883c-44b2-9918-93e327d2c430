{"logs": [{"outputFile": "com.aliduct.manager.app-mergeDebugResources-15:/values-km/values-km.xml", "map": [{"source": "D:\\.gradle\\caches\\transforms-3\\7bf4e412d2f87008824f0514de55aeec\\transformed\\appcompat-1.4.2\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}}, {"source": "D:\\.gradle\\caches\\transforms-3\\7203a9e46e7bafeda81451f706b02bf0\\transformed\\core-1.7.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2877", "endColumns": "100", "endOffsets": "2973"}}]}]}