{"logs": [{"outputFile": "com.aliduct.manager.app-mergeDebugResources-15:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "D:\\.gradle\\caches\\transforms-3\\7bf4e412d2f87008824f0514de55aeec\\transformed\\appcompat-1.4.2\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}}, {"source": "D:\\.gradle\\caches\\transforms-3\\7203a9e46e7bafeda81451f706b02bf0\\transformed\\core-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2922", "endColumns": "100", "endOffsets": "3018"}}]}]}