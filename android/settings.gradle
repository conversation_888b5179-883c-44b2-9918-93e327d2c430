pluginManagement {
  repositories {
    gradlePluginPortal()
    google()
    mavenCentral()
  }

  def expoPluginsPath = new File(
    providers.exec {
      workingDir(rootDir)
      commandLine("node", "--print", "require.resolve('expo-modules-autolinking/package.json', { paths: [require.resolve('expo/package.json')] })")
    }.standardOutput.asText.get().trim(),
    "../android/expo-gradle-plugin"
  ).absolutePath
  includeBuild(expoPluginsPath)
}

plugins {
  id("expo-autolinking-settings")
}

// React Native autolinking configuration removed for React Native 0.73.9 compatibility
expoAutolinking.useExpoModules()

rootProject.name = 'Ali duct factory'

expoAutolinking.useExpoVersionCatalog()

include ':app'
