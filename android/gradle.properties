# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# Optimized for React Native builds
org.gradle.jvmargs=-Xmx6144m -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC

# Enable Gradle features for better performance
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true
org.gradle.caching=true

# AndroidX package structure
android.useAndroidX=true

# Enable AAPT2 PNG crunching
android.enablePngCrunchInReleaseBuilds=true

# Disable unnecessary features for faster builds
android.enableJetifier=false
android.enableR8.fullMode=false

# Architecture configuration - focus on common architectures
reactNativeArchitectures=armeabi-v7a,arm64-v8a

# Disable new architecture for stability
newArchEnabled=false

# Use Hermes for better performance
hermesEnabled=true

# Expo image format support
expo.gif.enabled=true
expo.webp.enabled=true
expo.webp.animated=false

# Development features
EX_DEV_CLIENT_NETWORK_INSPECTOR=true

# Use modern packaging
expo.useLegacyPackaging=false

# Additional optimizations
android.enableBuildCache=true
android.enableSeparateAnnotationProcessing=true
