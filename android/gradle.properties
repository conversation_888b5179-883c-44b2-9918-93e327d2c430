org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.daemon=true
org.gradle.configureondemand=false

android.useAndroidX=true
android.enableJetifier=false
android.enableR8.fullMode=false
android.enableProguardInReleaseBuilds=false

reactNativeArchitectures=armeabi-v7a,arm64-v8a
hermesEnabled=true
enableHermes=true

newArchEnabled=false
expo.jsEngine=hermes

FLIPPER_VERSION=0.182.0

# Safe build properties
android.useAndroidX=true
android.enableJetifier=true
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.daemon=true
android.compileSdkVersion=34
android.targetSdkVersion=34
android.minSdkVersion=21
