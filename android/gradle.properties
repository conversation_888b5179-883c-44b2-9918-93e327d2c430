# Gradle settings - Use existing D drive cache to save C drive space
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true
org.gradle.caching=true

# Force Gradle to use D drive cache directory
org.gradle.user.home=D:\\.gradle

# Additional cache and temp directories on D drive
gradle.user.home=D:\\.gradle
org.gradle.cache.dir=D:\\.gradle\\caches
org.gradle.wrapper.dir=D:\\.gradle\\wrapper

# Android settings
android.useAndroidX=true
android.enableJetifier=true

# React Native settings
newArchEnabled=false
hermesEnabled=true

# Optimize build performance with existing cache
org.gradle.unsafe.configuration-cache=true
org.gradle.configuration-cache.problems=warn