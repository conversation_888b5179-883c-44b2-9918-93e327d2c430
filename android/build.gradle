// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
  repositories {
    google()
    mavenCentral()
  }
  dependencies {
    // Use AGP 8.5.2 for better compatibility with Gradle 8.10.2
    classpath('com.android.tools.build:gradle:8.5.2')
    classpath('com.facebook.react:react-native-gradle-plugin')
    classpath('org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.24')
  }
}

def reactNativeAndroidDir = new File(
  providers.exec {
    workingDir(rootDir)
    commandLine("node", "--print", "require.resolve('react-native/package.json')")
  }.standardOutput.asText.get().trim(),
  "../android"
)

allprojects {
  repositories {
    maven {
      // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
      url(reactNativeAndroidDir)
    }

    google()
    mavenCentral()
    maven { url 'https://www.jitpack.io' }
    
    // Add additional repositories for better dependency resolution
    maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
  }
  
  // Force consistent versions for common dependencies
  configurations.all {
    resolutionStrategy {
      force 'androidx.core:core:1.12.0'
      force 'androidx.appcompat:appcompat:1.6.1'
      force 'androidx.fragment:fragment:1.6.2'
    }
  }
}

apply plugin: "expo-root-project"
apply plugin: "com.facebook.react.rootproject"
