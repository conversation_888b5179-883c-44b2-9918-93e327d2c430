#!/usr/bin/env node

/**
 * Comprehensive Gradle Fix and Local Build Script
 * 
 * This script addresses the Gradle build failures and implements
 * alternative build methods to generate a working APK file.
 * 
 * Issues addressed:
 * 1. Gradle version compatibility
 * 2. Android Gradle Plugin compatibility
 * 3. React Native 0.79 + Expo SDK 53 configuration
 * 4. Local development build setup
 * 5. Alternative build methods
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Starting Comprehensive Gradle Fix and Local Build Setup...\n');

// Backup original files
function createBackup(filePath) {
    if (fs.existsSync(filePath)) {
        const backupPath = `${filePath}.backup-${Date.now()}`;
        fs.copyFileSync(filePath, backupPath);
        console.log(`✅ Backup created: ${backupPath}`);
        return backupPath;
    }
    return null;
}

// 1. Fix Android Gradle Plugin and Gradle Wrapper compatibility
console.log('1️⃣ Fixing Gradle and AGP compatibility...');

const androidBuildGradle = path.join(__dirname, 'android', 'build.gradle');
const gradleWrapperProps = path.join(__dirname, 'android', 'gradle', 'wrapper', 'gradle-wrapper.properties');
const gradleProperties = path.join(__dirname, 'android', 'gradle.properties');

// Backup files
createBackup(androidBuildGradle);
createBackup(gradleWrapperProps);
createBackup(gradleProperties);

// Fix root build.gradle - Use compatible AGP version
const rootBuildGradleContent = `// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
  repositories {
    google()
    mavenCentral()
  }
  dependencies {
    // Use AGP 8.5.2 for better compatibility with Gradle 8.10.2
    classpath('com.android.tools.build:gradle:8.5.2')
    classpath('com.facebook.react:react-native-gradle-plugin')
    classpath('org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.24')
  }
}

def reactNativeAndroidDir = new File(
  providers.exec {
    workingDir(rootDir)
    commandLine("node", "--print", "require.resolve('react-native/package.json')")
  }.standardOutput.asText.get().trim(),
  "../android"
)

allprojects {
  repositories {
    maven {
      // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
      url(reactNativeAndroidDir)
    }

    google()
    mavenCentral()
    maven { url 'https://www.jitpack.io' }
    
    // Add additional repositories for better dependency resolution
    maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
  }
  
  // Force consistent versions for common dependencies
  configurations.all {
    resolutionStrategy {
      force 'androidx.core:core:1.12.0'
      force 'androidx.appcompat:appcompat:1.6.1'
      force 'androidx.fragment:fragment:1.6.2'
    }
  }
}

apply plugin: "expo-root-project"
apply plugin: "com.facebook.react.rootproject"
`;

fs.writeFileSync(androidBuildGradle, rootBuildGradleContent);
console.log('✅ Updated android/build.gradle with compatible AGP version');

// Fix gradle-wrapper.properties - Use Gradle 8.8 for better compatibility
const gradleWrapperContent = `distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\\://services.gradle.org/distributions/gradle-8.8-all.zip
networkTimeout=10000
validateDistributionUrl=true
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
`;

fs.writeFileSync(gradleWrapperProps, gradleWrapperContent);
console.log('✅ Updated Gradle wrapper to version 8.8');

// 2. Fix gradle.properties for better performance and compatibility
console.log('\n2️⃣ Optimizing gradle.properties...');

const gradlePropsContent = `# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# Optimized for React Native builds
org.gradle.jvmargs=-Xmx6144m -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC

# Enable Gradle features for better performance
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true
org.gradle.caching=true

# AndroidX package structure
android.useAndroidX=true

# Enable AAPT2 PNG crunching
android.enablePngCrunchInReleaseBuilds=true

# Disable unnecessary features for faster builds
android.enableJetifier=false
android.enableR8.fullMode=false

# Architecture configuration - focus on common architectures
reactNativeArchitectures=armeabi-v7a,arm64-v8a

# Disable new architecture for stability
newArchEnabled=false

# Use Hermes for better performance
hermesEnabled=true

# Expo image format support
expo.gif.enabled=true
expo.webp.enabled=true
expo.webp.animated=false

# Development features
EX_DEV_CLIENT_NETWORK_INSPECTOR=true

# Use modern packaging
expo.useLegacyPackaging=false

# Additional optimizations
android.enableBuildCache=true
android.enableSeparateAnnotationProcessing=true
`;

fs.writeFileSync(gradleProperties, gradlePropsContent);
console.log('✅ Optimized gradle.properties for better performance');

// 3. Create local development build configuration
console.log('\n3️⃣ Setting up local development build...');

const localBuildScript = `#!/bin/bash

# Local Development Build Script for Ali Duct Factory
# This script builds the app locally using Expo development build

set -e

echo "🚀 Starting Local Development Build..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf android/app/build
rm -rf android/build
rm -rf .expo
rm -rf node_modules/.cache

# Ensure dependencies are installed
echo "📦 Installing dependencies..."
npm ci

# Clear Metro cache
echo "🗑️ Clearing Metro cache..."
npx expo start --clear

# Generate development build
echo "🔨 Building development APK..."
npx expo run:android --variant debug

echo "✅ Local development build completed!"
echo "📱 APK location: android/app/build/outputs/apk/debug/app-debug.apk"
`;

fs.writeFileSync(path.join(__dirname, 'build-local-dev.sh'), localBuildScript);
fs.chmodSync(path.join(__dirname, 'build-local-dev.sh'), '755');
console.log('✅ Created local development build script');

// 4. Create alternative EAS build configuration
console.log('\n4️⃣ Creating alternative EAS build configuration...');

const easConfig = JSON.parse(fs.readFileSync(path.join(__dirname, 'eas.json'), 'utf8'));

// Add a new stable build profile
easConfig.build.stable = {
  "extends": "preview",
  "android": {
    "buildType": "apk",
    "gradleCommand": ":app:assembleRelease",
    "env": {
      "EXPO_USE_FAST_RESOLVER": "1"
    }
  },
  "cache": {
    "disabled": false
  }
};

// Update preview profile for better stability
easConfig.build.preview.android = {
  ...easConfig.build.preview.android,
  "gradleCommand": ":app:assembleDebug",
  "buildType": "apk"
};

fs.writeFileSync(path.join(__dirname, 'eas.json'), JSON.stringify(easConfig, null, 2));
console.log('✅ Updated EAS configuration with stable build profile');

console.log('\n🎯 Build Methods Available:');
console.log('1. Local Development Build: ./build-local-dev.sh');
console.log('2. EAS Stable Build: eas build --profile stable');
console.log('3. Manual Android Studio Build: Open android/ folder in Android Studio');
console.log('4. Expo Development Client: npx expo run:android');

console.log('\n✅ Gradle fixes and build setup completed!');
console.log('\n📋 Next Steps:');
console.log('1. Run: chmod +x build-local-dev.sh');
console.log('2. Run: ./build-local-dev.sh');
console.log('3. If local build fails, try: eas build --profile stable');
