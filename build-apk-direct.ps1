#!/usr/bin/env pwsh

# Direct APK Build Script for Ali Duct Factory
# This script builds the APK directly using Gradle without requiring a connected device

Write-Host "🚀 Ali Duct Factory - Direct APK Build" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java not found. Please install Java 17" -ForegroundColor Red
    exit 1
}

# Check Android SDK
if (-not $env:ANDROID_HOME) {
    Write-Host "❌ ANDROID_HOME not set" -ForegroundColor Red
    exit 1
} else {
    Write-Host "✅ Android SDK: $env:ANDROID_HOME" -ForegroundColor Green
}

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow

if (Test-Path "android/app/build") {
    Remove-Item -Recurse -Force "android/app/build"
    Write-Host "   Removed android/app/build" -ForegroundColor Cyan
}

if (Test-Path "android/build") {
    Remove-Item -Recurse -Force "android/build"
    Write-Host "   Removed android/build" -ForegroundColor Cyan
}

if (Test-Path ".expo") {
    Remove-Item -Recurse -Force ".expo"
    Write-Host "   Removed .expo cache" -ForegroundColor Cyan
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm ci
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

# Fix Expo dependencies
Write-Host "⚙️  Fixing Expo dependencies..." -ForegroundColor Yellow
npx expo install --fix
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to fix Expo dependencies" -ForegroundColor Red
    exit 1
}

# Pre-build the Expo app to generate necessary files
Write-Host "🔧 Pre-building Expo app..." -ForegroundColor Yellow
npx expo prebuild --platform android --clear
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to prebuild Expo app" -ForegroundColor Red
    exit 1
}

# Build the APK using Gradle directly
Write-Host "🔨 Building APK using Gradle..." -ForegroundColor Yellow
Write-Host "   This may take several minutes..." -ForegroundColor Cyan

Set-Location "android"

# Try to build the debug APK
./gradlew assembleDebug --no-daemon --stacktrace

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Gradle build failed" -ForegroundColor Red
    Write-Host "💡 Trying alternative build method..." -ForegroundColor Yellow
    
    # Try with different Gradle options
    ./gradlew clean assembleDebug --no-daemon --no-build-cache --refresh-dependencies
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Alternative build also failed" -ForegroundColor Red
        Set-Location ".."
        exit 1
    }
}

Set-Location ".."

# Check for generated APK
$apkPath = "android/app/build/outputs/apk/debug/app-debug.apk"

if (Test-Path $apkPath) {
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
    Write-Host "📱 APK location: $apkPath" -ForegroundColor Cyan
    
    $apkSize = (Get-Item $apkPath).Length / 1MB
    Write-Host "📊 APK size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "🎯 Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Copy APK to your Android device: $apkPath" -ForegroundColor Cyan
    Write-Host "2. Enable 'Install from unknown sources' in Android settings" -ForegroundColor Cyan
    Write-Host "3. Install the APK file on your device" -ForegroundColor Cyan
    Write-Host "4. Launch 'Ali Duct Factory' app" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "📋 Installation Instructions:" -ForegroundColor Yellow
    Write-Host "- On your Android device, go to Settings > Security" -ForegroundColor Cyan
    Write-Host "- Enable 'Unknown sources' or 'Install unknown apps'" -ForegroundColor Cyan
    Write-Host "- Transfer the APK file to your device via USB or email" -ForegroundColor Cyan
    Write-Host "- Tap the APK file to install" -ForegroundColor Cyan
    Write-Host "- Follow the installation prompts" -ForegroundColor Cyan
    
} else {
    Write-Host "❌ APK file not found at expected location: $apkPath" -ForegroundColor Red
    Write-Host "🔍 Searching for APK files..." -ForegroundColor Yellow
    Get-ChildItem -Path "android" -Filter "*.apk" -Recurse | ForEach-Object {
        Write-Host "   Found: $($_.FullName)" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "🏁 Build process completed!" -ForegroundColor Green
