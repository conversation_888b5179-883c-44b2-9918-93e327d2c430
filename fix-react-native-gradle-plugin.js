#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing React Native Gradle Plugin Compatibility Issue...');

// Fix 1: Update package.json to use a compatible React Native Gradle plugin version
const packageJsonPath = 'package.json';
if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Force a compatible version of React Native Gradle plugin
    if (!packageJson.resolutions) packageJson.resolutions = {};
    if (!packageJson.overrides) packageJson.overrides = {};
    
    packageJson.resolutions['@react-native/gradle-plugin'] = '0.79.1';
    packageJson.overrides['@react-native/gradle-plugin'] = '0.79.1';
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Updated package.json with React Native Gradle plugin version override');
}

// Fix 2: Update android/build.gradle to use compatible versions
const androidBuildGradlePath = 'android/build.gradle';
if (fs.existsSync(androidBuildGradlePath)) {
    let content = fs.readFileSync(androidBuildGradlePath, 'utf8');
    
    // Update Android Gradle Plugin to a more stable version
    content = content.replace(
        /com\.android\.tools\.build:gradle:[^'"]*/g,
        'com.android.tools.build:gradle:8.1.4'
    );
    
    // Ensure we're using compatible Kotlin version
    content = content.replace(
        /org\.jetbrains\.kotlin:kotlin-gradle-plugin:[^'"]*/g,
        'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.22'
    );
    
    fs.writeFileSync(androidBuildGradlePath, content);
    console.log('✅ Updated android/build.gradle with compatible versions');
}

// Fix 3: Update Gradle wrapper to compatible version
const gradleWrapperPath = 'android/gradle/wrapper/gradle-wrapper.properties';
if (fs.existsSync(gradleWrapperPath)) {
    let content = fs.readFileSync(gradleWrapperPath, 'utf8');
    content = content.replace(
        /distributionUrl=.*/,
        'distributionUrl=https\\://services.gradle.org/distributions/gradle-8.1.1-bin.zip'
    );
    fs.writeFileSync(gradleWrapperPath, content);
    console.log('✅ Updated Gradle wrapper to version 8.1.1');
}

// Fix 4: Update gradle.properties for better compatibility
const gradlePropertiesPath = 'android/gradle.properties';
if (fs.existsSync(gradlePropertiesPath)) {
    let content = fs.readFileSync(gradlePropertiesPath, 'utf8');
    
    // Remove problematic properties that might cause issues
    const linesToRemove = [
        'enableWarningsAsErrors',
        'android.experimental.enableSourceSetPathsMap',
        'android.experimental.enableNewResourceShrinker'
    ];
    
    linesToRemove.forEach(line => {
        content = content.replace(new RegExp(`^${line}=.*$`, 'gm'), '');
    });
    
    // Add safe properties
    const safeProperties = [
        '',
        '# Safe build properties',
        'android.useAndroidX=true',
        'android.enableJetifier=true',
        'org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m',
        'org.gradle.parallel=true',
        'org.gradle.caching=true',
        'org.gradle.daemon=true',
        'android.compileSdkVersion=34',
        'android.targetSdkVersion=34',
        'android.minSdkVersion=21'
    ];
    
    // Remove duplicate empty lines and add our properties
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    content += '\n' + safeProperties.join('\n') + '\n';
    
    fs.writeFileSync(gradlePropertiesPath, content);
    console.log('✅ Updated gradle.properties with safe build settings');
}

// Fix 5: Clean up problematic cache and build files
const pathsToClean = [
    'android/app/build',
    'android/build',
    '.expo',
    'node_modules/.cache',
    'android/.gradle'
];

pathsToClean.forEach(dirPath => {
    if (fs.existsSync(dirPath)) {
        try {
            fs.rmSync(dirPath, { recursive: true, force: true });
            console.log(`✅ Cleaned ${dirPath}`);
        } catch (error) {
            console.log(`⚠️  Could not clean ${dirPath}: ${error.message}`);
        }
    }
});

console.log('');
console.log('🎯 React Native Gradle Plugin Fix Complete!');
console.log('');
console.log('Next steps:');
console.log('1. Run: npm install');
console.log('2. Run: npx expo run:android --device');
console.log('');
