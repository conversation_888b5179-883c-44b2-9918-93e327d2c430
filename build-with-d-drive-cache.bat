@echo off
echo 🔧 Ali Duct Factory - Build with D Drive Gradle Cache
echo ======================================================

REM Set environment variables to force <PERSON><PERSON><PERSON> to use D drive
set GRADLE_USER_HOME=D:\.gradle
set GRADLE_CACHE_DIR=D:\.gradle\caches
set GRADLE_WRAPPER_DIR=D:\.gradle\wrapper

echo 📁 Gradle cache directory: %GRADLE_USER_HOME%
echo 📁 Gradle cache dir: %GRADLE_CACHE_DIR%
echo 📁 Gradle wrapper dir: %GRADLE_WRAPPER_DIR%

REM Check if D:\.gradle exists
if exist "D:\.gradle" (
    echo ✅ Found existing Gradle cache at D:\.gradle
) else (
    echo ⚠️  D:\.gradle directory not found, will be created
)

REM Check device connection
echo 📱 Checking Android device connection...
adb devices | findstr "device" >nul
if %errorlevel% equ 0 (
    echo ✅ Android device detected
) else (
    echo ❌ No Android device detected. Please connect your device and enable USB debugging.
    pause
    exit /b 1
)

REM Clean previous builds
echo 🧹 Cleaning previous builds...
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist "android\build" rmdir /s /q "android\build"

REM Method 1: Try direct Gradle build with environment variables
echo 🔨 Method 1: Direct Gradle build (using D drive cache)...
cd android

echo 🧹 Gradle clean...
gradlew clean --gradle-user-home="D:\.gradle"

echo 🔨 Building debug APK...
gradlew assembleDebug --gradle-user-home="D:\.gradle" --no-daemon --parallel

cd ..

REM Check for APK
if exist "android\app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ APK built successfully!
    echo 📱 APK location: android\app\build\outputs\apk\debug\app-debug.apk
    
    REM Install APK
    echo 📲 Installing APK to device...
    adb install -r "android\app\build\outputs\apk\debug\app-debug.apk"
    echo ✅ APK installed successfully!
    echo 🎯 You can now launch 'Ali Duct Factory' on your device
    goto :success
)

REM Method 2: Try Expo run:android
echo 🔨 Method 2: Building with Expo (using D drive cache)...
npx expo run:android --device --no-build-cache
if %errorlevel% equ 0 (
    echo ✅ Expo build completed successfully!
    goto :success
)

REM Method 3: React Native CLI
echo 🔨 Method 3: React Native CLI build...
npx react-native run-android --variant=debug
if %errorlevel% equ 0 (
    echo ✅ React Native CLI build completed!
    goto :success
)

echo ❌ All automated build methods failed
echo.
echo 📋 Manual build instructions:
echo 1. Open Android Studio
echo 2. Open the android folder as a project
echo 3. In Android Studio, go to File ^> Settings ^> Build ^> Gradle
echo 4. Set Gradle user home to: D:\.gradle
echo 5. Build ^> Generate Signed Bundle / APK ^> APK ^> Debug
echo 6. Install the generated APK on your device
echo.
echo 💡 Your existing Gradle cache at D:\.gradle will be used to save space

:success
echo 🏁 Build process completed!
pause
