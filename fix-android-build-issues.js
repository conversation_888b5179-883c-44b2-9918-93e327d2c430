/**
 * Android Build Issues Diagnostic and Fix Script
 * Identifies and fixes critical issues causing app crashes
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Android Build Issues Diagnostic and Fix Script');
console.log('================================================\n');

// Check for critical configuration mismatches
function checkConfigurationMismatches() {
  console.log('1️⃣ CHECKING CONFIGURATION MISMATCHES...\n');
  
  try {
    // Read app.json
    const appJsonPath = './app.json';
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    // Read build.gradle
    const buildGradlePath = './android/app/build.gradle';
    const buildGradle = fs.readFileSync(buildGradlePath, 'utf8');
    
    // Extract package IDs
    const appJsonPackage = appJson.expo.android.package;
    const buildGradleMatch = buildGradle.match(/applicationId\s+"([^"]+)"/);
    const namespaceMatch = buildGradle.match(/namespace\s+"([^"]+)"/);
    
    console.log('📦 Package ID Analysis:');
    console.log(`   app.json package: ${appJsonPackage}`);
    console.log(`   build.gradle applicationId: ${buildGradleMatch ? buildGradleMatch[1] : 'NOT FOUND'}`);
    console.log(`   build.gradle namespace: ${namespaceMatch ? namespaceMatch[1] : 'NOT FOUND'}`);
    
    if (buildGradleMatch && buildGradleMatch[1] !== appJsonPackage) {
      console.log('❌ CRITICAL: Package ID mismatch detected!');
      console.log('   This WILL cause app crashes on startup.\n');
      return false;
    } else {
      console.log('✅ Package IDs match correctly.\n');
      return true;
    }
    
  } catch (error) {
    console.log(`❌ Error checking configurations: ${error.message}\n`);
    return false;
  }
}

// Check Android permissions
function checkAndroidPermissions() {
  console.log('2️⃣ CHECKING ANDROID PERMISSIONS...\n');
  
  try {
    // Read AndroidManifest.xml
    const manifestPath = './android/app/src/main/AndroidManifest.xml';
    const manifest = fs.readFileSync(manifestPath, 'utf8');
    
    // Read app.json for expected permissions
    const appJsonPath = './app.json';
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    const expectedPermissions = appJson.expo.android.permissions || [];
    const manifestPermissions = [];
    
    // Extract permissions from manifest
    const permissionMatches = manifest.match(/<uses-permission[^>]+android:name="([^"]+)"/g);
    if (permissionMatches) {
      permissionMatches.forEach(match => {
        const permission = match.match(/android:name="([^"]+)"/)[1];
        manifestPermissions.push(permission);
      });
    }
    
    console.log('📱 Permission Analysis:');
    console.log(`   Expected permissions (${expectedPermissions.length}):`, expectedPermissions);
    console.log(`   Manifest permissions (${manifestPermissions.length}):`, manifestPermissions);
    
    // Check for missing critical permissions
    const missingPermissions = expectedPermissions.filter(perm => 
      !manifestPermissions.includes(perm)
    );
    
    if (missingPermissions.length > 0) {
      console.log('❌ CRITICAL: Missing permissions in AndroidManifest.xml:');
      missingPermissions.forEach(perm => console.log(`     - ${perm}`));
      console.log('   This can cause crashes when accessing camera/location.\n');
      return false;
    } else {
      console.log('✅ All required permissions are present.\n');
      return true;
    }
    
  } catch (error) {
    console.log(`❌ Error checking permissions: ${error.message}\n`);
    return false;
  }
}

// Check New Architecture compatibility
function checkNewArchitecture() {
  console.log('3️⃣ CHECKING NEW ARCHITECTURE COMPATIBILITY...\n');
  
  try {
    // Read app.json
    const appJsonPath = './app.json';
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    // Read gradle.properties
    const gradlePropsPath = './android/gradle.properties';
    const gradleProps = fs.readFileSync(gradlePropsPath, 'utf8');
    
    const appJsonNewArch = appJson.expo.newArchEnabled;
    const gradleNewArch = gradleProps.includes('newArchEnabled=true');
    
    console.log('🏗️ New Architecture Analysis:');
    console.log(`   app.json newArchEnabled: ${appJsonNewArch}`);
    console.log(`   gradle.properties newArchEnabled: ${gradleNewArch}`);
    
    if (appJsonNewArch || gradleNewArch) {
      console.log('⚠️  WARNING: New Architecture is enabled!');
      console.log('   This can cause crashes with incompatible dependencies.');
      console.log('   Consider disabling for production builds.\n');
      return false;
    } else {
      console.log('✅ New Architecture is disabled (recommended for stability).\n');
      return true;
    }
    
  } catch (error) {
    console.log(`❌ Error checking New Architecture: ${error.message}\n`);
    return false;
  }
}

// Check Google Maps configuration
function checkGoogleMapsConfig() {
  console.log('4️⃣ CHECKING GOOGLE MAPS CONFIGURATION...\n');
  
  try {
    const appJsonPath = './app.json';
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    const googleMapsConfig = appJson.expo.android.config?.googleMaps;
    
    console.log('🗺️ Google Maps Analysis:');
    if (googleMapsConfig && googleMapsConfig.apiKey) {
      if (googleMapsConfig.apiKey === 'YOUR_GOOGLE_MAPS_API_KEY') {
        console.log('❌ CRITICAL: Google Maps API key is placeholder!');
        console.log('   This will cause crashes when accessing location features.\n');
        return false;
      } else {
        console.log('✅ Google Maps API key is configured.\n');
        return true;
      }
    } else {
      console.log('⚠️  WARNING: No Google Maps configuration found.');
      console.log('   Location features may not work properly.\n');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Error checking Google Maps config: ${error.message}\n`);
    return false;
  }
}

// Check Supabase environment variables
function checkSupabaseConfig() {
  console.log('5️⃣ CHECKING SUPABASE CONFIGURATION...\n');
  
  try {
    // Check .env file
    const envPath = './.env';
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const hasUrl = envContent.includes('EXPO_PUBLIC_SUPABASE_URL=');
      const hasKey = envContent.includes('EXPO_PUBLIC_SUPABASE_ANON_KEY=');
      
      console.log('🗄️ Supabase Configuration Analysis:');
      console.log(`   .env file exists: ✅`);
      console.log(`   SUPABASE_URL configured: ${hasUrl ? '✅' : '❌'}`);
      console.log(`   SUPABASE_ANON_KEY configured: ${hasKey ? '✅' : '❌'}`);
      
      if (!hasUrl || !hasKey) {
        console.log('❌ CRITICAL: Missing Supabase configuration!');
        console.log('   This will cause crashes when accessing database.\n');
        return false;
      } else {
        console.log('✅ Supabase configuration looks good.\n');
        return true;
      }
    } else {
      console.log('❌ CRITICAL: .env file not found!');
      console.log('   Supabase configuration is missing.\n');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Error checking Supabase config: ${error.message}\n`);
    return false;
  }
}

// Main diagnostic function
async function runDiagnostics() {
  console.log('🚀 Starting comprehensive Android build diagnostics...\n');
  
  const results = {
    packageIds: checkConfigurationMismatches(),
    permissions: checkAndroidPermissions(),
    newArchitecture: checkNewArchitecture(),
    googleMaps: checkGoogleMapsConfig(),
    supabase: checkSupabaseConfig()
  };
  
  console.log('📊 DIAGNOSTIC SUMMARY:');
  console.log('======================');
  
  const issues = [];
  Object.entries(results).forEach(([check, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${check.padEnd(15)}: ${status}`);
    if (!passed) issues.push(check);
  });
  
  console.log(`\n🎯 RESULT: ${issues.length === 0 ? 'ALL CHECKS PASSED!' : `${issues.length} CRITICAL ISSUES FOUND`}`);
  
  if (issues.length > 0) {
    console.log('\n🔧 CRITICAL ISSUES TO FIX:');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
    
    console.log('\n💡 NEXT STEPS:');
    console.log('1. Run the fix script to automatically resolve issues');
    console.log('2. Test with a preview build before production');
    console.log('3. Check device logs for specific crash details');
  }
  
  return issues.length === 0;
}

// Run diagnostics
runDiagnostics().catch(console.error);
