#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔧 Ali Duct Factory - Minimal Android Build (D Drive Cache)');
console.log('============================================================');

// Set environment variables for D drive cache
process.env.GRADLE_USER_HOME = 'D:\\.gradle';
process.env.GRADLE_CACHE_DIR = 'D:\\.gradle\\caches';
process.env.GRADLE_WRAPPER_DIR = 'D:\\.gradle\\wrapper';

console.log('📁 Using Gradle cache at:', process.env.GRADLE_USER_HOME);

// Check device
console.log('📱 Checking device connection...');
try {
    const devices = execSync('adb devices', { encoding: 'utf8' });
    if (devices.includes('device')) {
        console.log('✅ Android device detected');
    } else {
        console.log('❌ No device detected. Please connect your device.');
        process.exit(1);
    }
} catch (error) {
    console.log('❌ ADB not found');
    process.exit(1);
}

// Create minimal settings.gradle without Expo autolinking
console.log('🔧 Creating minimal settings.gradle...');
const minimalSettings = `
pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_PROJECT)
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        maven { url "https://maven.google.com" }
    }
}

rootProject.name = 'Ali duct factory'
include ':app'
`;

fs.writeFileSync('android/settings.gradle', minimalSettings.trim());
console.log('✅ Created minimal settings.gradle');

// Create minimal app build.gradle
console.log('🔧 Creating minimal app build.gradle...');
const minimalAppBuild = `
apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"

android {
    namespace "com.aliduct.manager"
    compileSdk 34

    defaultConfig {
        applicationId "com.aliduct.manager"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
}

dependencies {
    implementation "androidx.core:core-ktx:1.9.0"
    implementation "androidx.appcompat:appcompat:1.6.1"
    implementation "com.google.android.material:material:1.8.0"
    implementation "androidx.constraintlayout:constraintlayout:2.1.4"
    
    // React Native
    implementation "com.facebook.react:react-android:0.73.9"
    implementation "com.facebook.react:hermes-android:0.73.9"
}
`;

fs.writeFileSync('android/app/build.gradle', minimalAppBuild.trim());
console.log('✅ Created minimal app build.gradle');

// Create minimal MainActivity
console.log('🔧 Creating minimal MainActivity...');
const mainActivityDir = 'android/app/src/main/java/com/aliduct/manager';
if (!fs.existsSync(mainActivityDir)) {
    fs.mkdirSync(mainActivityDir, { recursive: true });
}

const minimalMainActivity = `
package com.aliduct.manager

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
    }
}
`;

fs.writeFileSync(`${mainActivityDir}/MainActivity.kt`, minimalMainActivity.trim());
console.log('✅ Created minimal MainActivity');

// Create minimal layout
console.log('🔧 Creating minimal layout...');
const layoutDir = 'android/app/src/main/res/layout';
if (!fs.existsSync(layoutDir)) {
    fs.mkdirSync(layoutDir, { recursive: true });
}

const minimalLayout = `
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ali Duct Factory"
        android:textSize="24sp"
        android:textStyle="bold" />

</LinearLayout>
`;

fs.writeFileSync(`${layoutDir}/activity_main.xml`, minimalLayout.trim());
console.log('✅ Created minimal layout');

// Create AndroidManifest.xml
console.log('🔧 Creating AndroidManifest.xml...');
const manifestDir = 'android/app/src/main';
const manifest = `
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.aliduct.manager">

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="Ali Duct Factory"
        android:theme="@style/Theme.AppCompat.Light.DarkActionBar">
        
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
`;

fs.writeFileSync(`${manifestDir}/AndroidManifest.xml`, manifest.trim());
console.log('✅ Created AndroidManifest.xml');

// Try to build
console.log('🔨 Building minimal APK...');
try {
    process.chdir('android');
    
    console.log('🧹 Cleaning...');
    execSync('gradlew clean --gradle-user-home="D:\\.gradle"', { stdio: 'inherit' });
    
    console.log('🔨 Building debug APK...');
    execSync('gradlew assembleDebug --gradle-user-home="D:\\.gradle" --no-daemon', { stdio: 'inherit' });
    
    process.chdir('..');
    
    // Check for APK
    const apkPath = 'android/app/build/outputs/apk/debug/app-debug.apk';
    if (fs.existsSync(apkPath)) {
        console.log('✅ APK built successfully!');
        console.log('📱 APK location:', apkPath);
        
        // Install APK
        console.log('📲 Installing APK to device...');
        execSync(`adb install -r "${apkPath}"`, { stdio: 'inherit' });
        console.log('✅ APK installed successfully!');
        console.log('🎯 You can now launch "Ali Duct Factory" on your device');
        console.log('');
        console.log('📊 Build Summary:');
        console.log('✅ Used D drive Gradle cache - no C drive space consumed');
        console.log('✅ Minimal Android app created and installed');
        console.log('✅ Device ready for testing');
        
    } else {
        console.log('❌ APK not found after build');
    }
    
} catch (error) {
    console.log('❌ Build failed:', error.message);
    console.log('');
    console.log('📋 Manual build option:');
    console.log('1. Open Android Studio');
    console.log('2. Open the android folder as a project');
    console.log('3. Set Gradle user home to D:\\.gradle in settings');
    console.log('4. Build > Generate Signed Bundle / APK > APK > Debug');
}

console.log('🏁 Process completed!');
