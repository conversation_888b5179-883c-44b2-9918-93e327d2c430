#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

console.log('🔧 Ali Duct Factory - Build with System Gradle');
console.log('===============================================');

// Check if device is connected
console.log('📱 Checking device connection...');
try {
    const devices = execSync('adb devices', { encoding: 'utf8' });
    if (devices.includes('device')) {
        console.log('✅ Android device detected');
    } else {
        console.log('❌ No Android device detected. Please connect your device.');
        process.exit(1);
    }
} catch (error) {
    console.log('❌ ADB not found or device not connected');
    process.exit(1);
}

// Configure Gradle to use system installation
console.log('🔧 Configuring Gradle to use system installation...');

// Update gradle-wrapper.properties to use system Gradle
const gradleWrapperPath = 'android/gradle/wrapper/gradle-wrapper.properties';
if (fs.existsSync(gradleWrapperPath)) {
    const gradleWrapperContent = `
distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\\://services.gradle.org/distributions/gradle-8.0.2-all.zip
networkTimeout=10000
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
`;
    fs.writeFileSync(gradleWrapperPath, gradleWrapperContent.trim());
    console.log('✅ Updated gradle-wrapper.properties');
}

// Update gradle.properties to use your system Gradle cache
const gradlePropsPath = 'android/gradle.properties';
const gradleProps = `
# Gradle settings
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true
org.gradle.caching=true

# Android settings
android.useAndroidX=true
android.enableJetifier=true

# React Native settings
newArchEnabled=false
hermesEnabled=true

# Use system Gradle cache
org.gradle.user.home=D:\\.gradle
`;

fs.writeFileSync(gradlePropsPath, gradleProps.trim());
console.log('✅ Updated gradle.properties to use system Gradle cache');

// Update Android build.gradle with compatible versions
console.log('🔧 Updating Android build configuration...');
const androidBuildGradle = `
buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.8.10"
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.0.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:\$kotlinVersion")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
    }
}
`;

fs.writeFileSync('android/build.gradle', androidBuildGradle.trim());
console.log('✅ Updated android/build.gradle');

// Try different build approaches
console.log('🔨 Attempting to build APK...');

// Method 1: Try using system Gradle directly
console.log('📦 Method 1: Using system Gradle...');
try {
    process.chdir('android');
    
    // Clean first
    console.log('🧹 Cleaning previous builds...');
    execSync('gradle clean', { stdio: 'inherit' });
    
    // Build debug APK
    console.log('🔨 Building debug APK...');
    execSync('gradle assembleDebug --no-daemon --parallel', { stdio: 'inherit' });
    
    process.chdir('..');
    
    // Check for APK
    const apkPath = 'android/app/build/outputs/apk/debug/app-debug.apk';
    if (fs.existsSync(apkPath)) {
        console.log('✅ APK built successfully!');
        console.log('📱 APK location:', apkPath);
        
        // Install APK
        console.log('📲 Installing APK to device...');
        execSync(`adb install -r "${apkPath}"`, { stdio: 'inherit' });
        console.log('✅ APK installed successfully!');
        console.log('🎯 You can now launch "Ali Duct Factory" on your device');
        process.exit(0);
    }
    
} catch (error) {
    console.log('⚠️  System Gradle method failed, trying wrapper...');
    process.chdir('..');
}

// Method 2: Try using Gradle wrapper
console.log('📦 Method 2: Using Gradle wrapper...');
try {
    process.chdir('android');
    
    // Make gradlew executable
    if (process.platform !== 'win32') {
        execSync('chmod +x gradlew', { stdio: 'inherit' });
    }
    
    // Clean and build
    console.log('🧹 Cleaning with wrapper...');
    execSync('.\\gradlew clean', { stdio: 'inherit' });
    
    console.log('🔨 Building with wrapper...');
    execSync('.\\gradlew assembleDebug --no-daemon', { stdio: 'inherit' });
    
    process.chdir('..');
    
    // Check for APK
    const apkPath = 'android/app/build/outputs/apk/debug/app-debug.apk';
    if (fs.existsSync(apkPath)) {
        console.log('✅ APK built successfully with wrapper!');
        console.log('📱 APK location:', apkPath);
        
        // Install APK
        console.log('📲 Installing APK to device...');
        execSync(`adb install -r "${apkPath}"`, { stdio: 'inherit' });
        console.log('✅ APK installed successfully!');
        console.log('🎯 You can now launch "Ali Duct Factory" on your device');
        process.exit(0);
    }
    
} catch (error) {
    console.log('⚠️  Gradle wrapper method failed, trying Expo...');
    process.chdir('..');
}

// Method 3: Try Expo run:android
console.log('📦 Method 3: Using Expo run:android...');
try {
    execSync('npx expo run:android --device --no-build-cache', { stdio: 'inherit' });
    console.log('✅ Expo build completed!');
    
} catch (error) {
    console.log('❌ All build methods failed');
    console.log('');
    console.log('📋 Manual build instructions:');
    console.log('1. Open Android Studio');
    console.log('2. Open the android folder as a project');
    console.log('3. Let Android Studio sync and download dependencies');
    console.log('4. Build > Generate Signed Bundle / APK > APK');
    console.log('5. Choose debug build variant');
    console.log('6. Install the generated APK on your device');
    console.log('');
    console.log('💡 Your system Gradle cache at D:\\.gradle should help speed up the process');
}

console.log('🏁 Build process completed!');
